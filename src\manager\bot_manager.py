# src/manager/bot_manager.py
import asyncio
import importlib
from typing import Dict, Any, List
from manager.config_loader import Config, BotConfig
from core.interfaces import IBot, IStrategy, IBrokerage, BotStatus
from core.exceptions import (
    TradingBotNotFoundError, TradingBotAlreadyRunningError, TradingBotNotRunningError,
    ConfigurationError, StrategyError, BrokerageError
)
from services.database.factory import DatabaseFactory
from services.logging.factory import LoggingFactory


class BotManager:
    """Manager for orchestrating multiple trading bots"""

    def __init__(self, config: Config):
        self.config = config
        self.bots: Dict[str, IBot] = {}
        self.database = None
        self.logger = None
        self._initialize_manager()

    def _initialize_manager(self) -> None:
        """Initialize the manager's own resources"""
        # Setup logging
        self.logger = LoggingFactory.create_logger(
            "BotManager",
            self.config.manager.logging.model_dump()
        )

        # Setup database
        self.database = DatabaseFactory.create_database(
            self.config.manager.database.model_dump()
        )

        self.logger.info("Bot Manager initialized")

    async def initialize(self) -> None:
        """Initialize the manager and all configured bots"""
        self.logger.info("Initializing Bot Manager")

        try:
            # Connect manager database
            await self.database.connect()

            # Initialize all bots
            for bot_config in self.config.manager.bots:
                await self._create_bot(bot_config)

            self.logger.info(
                f"Bot Manager initialized with {len(self.bots)} bots")

        except Exception as e:
            self.logger.error(f"Failed to initialize Bot Manager: {e}")
            raise

    async def _create_bot(self, bot_config: BotConfig) -> None:
        """Create and configure a bot"""
        try:
            self.logger.info(f"Creating bot: {bot_config.name}")

            # Create bot logger
            bot_logger = LoggingFactory.create_logger(
                f"bot.{bot_config.name}",
                bot_config.logging.model_dump()
            )

            # Create bot database
            bot_database = DatabaseFactory.create_database(
                bot_config.database.model_dump())

            # Create strategy
            strategy = await self._create_strategy(bot_config.strategy)

            # Create brokerage
            brokerage = await self._create_brokerage(bot_config.brokerage)

            # Create bot instance
            bot_class = self._load_class(
                bot_config.module, bot_config.class_name)
            bot = bot_class(
                name=bot_config.name,
                config=bot_config.model_dump(),
                strategy=strategy,
                brokerage=brokerage,
                database=bot_database,
                logger=bot_logger
            )

            self.bots[bot_config.name] = bot
            self.logger.info(f"Bot {bot_config.name} created successfully")

        except Exception as e:
            self.logger.error(f"Failed to create bot {bot_config.name}: {e}")
            raise

    async def _create_strategy(self, strategy_config) -> IStrategy:
        """Create a strategy instance"""
        try:
            strategy_class = self._load_class(
                strategy_config.module,
                strategy_config.class_name
            )
            return strategy_class(strategy_config.params)
        except Exception as e:
            raise StrategyError(f"Failed to create strategy: {e}")

    async def _create_brokerage(self, brokerage_config) -> IBrokerage:
        """Create a brokerage instance"""
        try:
            brokerage_class = self._load_class(
                brokerage_config.module,
                brokerage_config.class_name
            )
            return brokerage_class(brokerage_config.params)
        except Exception as e:
            raise BrokerageError(f"Failed to create brokerage: {e}")

    def _load_class(self, module_name: str, class_name: str):
        """Dynamically load a class from a module"""
        try:
            module = importlib.import_module(module_name)
            return getattr(module, class_name)
        except ImportError as e:
            raise ConfigurationError(f"Module {module_name} not found: {e}")
        except AttributeError as e:
            raise ConfigurationError(
                f"Class {class_name} not found in {module_name}: {e}")

    async def start_bot(self, bot_name: str) -> None:
        """Start a specific bot"""
        bot = self._get_bot(bot_name)

        if bot.status == BotStatus.RUNNING:
            raise TradingBotAlreadyRunningError(f"Bot {bot_name} is already running")

        self.logger.info(f"Starting bot: {bot_name}")
        await bot.start()
        self.logger.info(f"Bot {bot_name} started successfully")

    async def stop_bot(self, bot_name: str) -> None:
        """Stop a specific bot"""
        bot = self._get_bot(bot_name)

        if bot.status != BotStatus.RUNNING:
            raise TradingBotNotRunningError(f"Bot {bot_name} is not running")

        self.logger.info(f"Stopping bot: {bot_name}")
        await bot.stop()
        self.logger.info(f"Bot {bot_name} stopped successfully")

    async def restart_bot(self, bot_name: str) -> None:
        """Restart a specific bot"""
        bot = self._get_bot(bot_name)

        if bot.status == BotStatus.RUNNING:
            await self.stop_bot(bot_name)

        await self.start_bot(bot_name)

    async def start_all_bots(self) -> None:
        """Start all bots"""
        self.logger.info("Starting all bots")

        tasks = []
        for bot_name, bot in self.bots.items():
            if bot.status != BotStatus.RUNNING:
                tasks.append(self.start_bot(bot_name))

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        self.logger.info("All bots start process completed")

    async def stop_all_bots(self) -> None:
        """Stop all bots"""
        self.logger.info("Stopping all bots")

        tasks = []
        for bot_name, bot in self.bots.items():
            if bot.status == BotStatus.RUNNING:
                tasks.append(self.stop_bot(bot_name))

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        self.logger.info("All bots stopped")

    def get_bot_status(self, bot_name: str) -> Dict[str, Any]:
        """Get status of a specific bot"""
        bot = self._get_bot(bot_name)
        return bot.get_status()

    def get_all_bots_status(self) -> List[Dict[str, Any]]:
        """Get status of all bots"""
        return [bot.get_status() for bot in self.bots.values()]

    def list_bots(self) -> List[str]:
        """List all bot names"""
        return list(self.bots.keys())

    def _get_bot(self, bot_name: str) -> IBot:
        """Get bot by name"""
        if bot_name not in self.bots:
            raise TradingBotNotFoundError(f"Bot {bot_name} not found")
        return self.bots[bot_name]

    async def shutdown(self) -> None:
        """Shutdown the manager and all bots"""
        self.logger.info("Shutting down Bot Manager")

        try:
            # Stop all bots
            await self.stop_all_bots()

            # Disconnect manager database
            if self.database:
                await self.database.disconnect()

            self.logger.info("Bot Manager shutdown completed")

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

    async def reload_config(self, config: Config) -> None:
        """Reload configuration (stops all bots and recreates them)"""
        self.logger.info("Reloading configuration")

        # Stop all current bots
        await self.stop_all_bots()

        # Clear current bots
        self.bots.clear()

        # Update config
        self.config = config

        # Reinitialize with new config
        await self.initialize()

        self.logger.info("Configuration reloaded successfully")

    async def get_bot_positions(self, bot_name: str) -> List[Dict[str, Any]]:
        """Get current positions for a specific bot"""
        bot = self._get_bot(bot_name)

        try:
            # Get account balance from the bot's brokerage
            if bot.brokerage:
                balances = await bot.brokerage.get_account_balance()
                positions = []

                for balance in balances:
                    # Only include assets with non-zero balances
                    if balance.total > 0:
                        positions.append({
                            "asset": balance.asset,
                            "free": balance.free,
                            "locked": balance.locked,
                            "total": balance.total
                        })

                self.logger.debug(f"Retrieved {len(positions)} positions for bot {bot_name}")
                return positions
            else:
                self.logger.warning(f"Bot {bot_name} has no brokerage configured")
                return []

        except Exception as e:
            self.logger.error(f"Failed to get positions for bot {bot_name}: {e}")
            raise

    async def emergency_stop_bot(self, bot_name: str) -> None:
        """Emergency stop for a specific bot (immediate shutdown)"""
        bot = self._get_bot(bot_name)

        try:
            self.logger.warning(f"Emergency stop initiated for bot: {bot_name}")

            # Cancel the bot's task immediately if it's running
            if bot._task and not bot._task.done():
                bot._task.cancel()
                try:
                    await bot._task
                except asyncio.CancelledError:
                    pass

            # Force status to stopped
            bot.status = BotStatus.STOPPED

            # Try to cancel any open orders if brokerage is available
            if bot.brokerage:
                try:
                    open_orders = await bot.brokerage.get_open_orders()
                    for order in open_orders:
                        try:
                            await bot.brokerage.cancel_order(order.id, order.symbol)
                            self.logger.info(f"Cancelled order {order.id} during emergency stop")
                        except Exception as cancel_error:
                            self.logger.error(f"Failed to cancel order {order.id}: {cancel_error}")
                except Exception as orders_error:
                    self.logger.error(f"Failed to retrieve/cancel orders during emergency stop: {orders_error}")

            self.logger.warning(f"Emergency stop completed for bot: {bot_name}")

        except Exception as e:
            self.logger.error(f"Error during emergency stop for bot {bot_name}: {e}")
            raise

    async def get_bot_orders(self, bot_name: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent orders for a specific bot"""
        bot = self._get_bot(bot_name)

        try:
            if not bot.brokerage:
                self.logger.warning(f"Bot {bot_name} has no brokerage configured")
                return []

            # Get order history from brokerage
            # Note: This assumes the bot trades a single symbol. For multi-symbol bots,
            # this would need to be enhanced to get orders for all symbols
            orders = []

            # Try to get open orders first
            try:
                open_orders = await bot.brokerage.get_open_orders()
                for order in open_orders:
                    orders.append({
                        "id": order.id,
                        "symbol": order.symbol,
                        "side": order.side.value,
                        "type": order.order_type.value,
                        "quantity": order.quantity,
                        "price": order.price,
                        "status": order.status,
                        "filled_quantity": order.filled_quantity,
                        "created_at": order.created_at.isoformat() if order.created_at else None,
                        "updated_at": order.updated_at.isoformat() if order.updated_at else None,
                        "client_order_id": order.client_order_id
                    })
            except Exception as e:
                self.logger.error(f"Failed to get open orders for bot {bot_name}: {e}")

            # If we need more orders to reach the limit, get order history
            # This is a simplified approach - in practice, you'd need to know which symbols the bot trades
            remaining_limit = limit - len(orders)
            if remaining_limit > 0:
                # For now, we'll just return the open orders
                # A more complete implementation would require tracking which symbols each bot trades
                pass

            # Limit the results
            orders = orders[:limit]

            self.logger.debug(f"Retrieved {len(orders)} orders for bot {bot_name}")
            return orders

        except Exception as e:
            self.logger.error(f"Failed to get orders for bot {bot_name}: {e}")
            raise

    async def get_bot_metrics(self, bot_name: str) -> Dict[str, Any]:
        """Get performance metrics for a specific bot"""
        bot = self._get_bot(bot_name)

        try:
            metrics = {
                "bot_name": bot_name,
                "status": bot.status.value,
                "uptime": None,
                "error_count": 0,
                "strategy_metrics": {},
                "account_metrics": {}
            }

            # Get basic bot status
            bot_status = bot.get_status()
            if isinstance(bot_status, dict):
                metrics.update({
                    "uptime": bot_status.get("uptime"),
                    "error_count": bot_status.get("error_count", 0),
                    "last_activity": bot_status.get("last_activity")
                })

            # Get strategy metrics if available
            if bot.strategy:
                try:
                    strategy_status = await bot.strategy.get_strategy_status()
                    metrics["strategy_metrics"] = strategy_status
                except Exception as e:
                    self.logger.error(f"Failed to get strategy metrics for bot {bot_name}: {e}")
                    metrics["strategy_metrics"] = {"error": str(e)}

            # Get account metrics if brokerage is available
            if bot.brokerage:
                try:
                    balances = await bot.brokerage.get_account_balance()
                    total_value = 0
                    asset_count = 0

                    for balance in balances:
                        if balance.total > 0:
                            asset_count += 1
                            # For simplicity, we're just counting assets
                            # In a real implementation, you'd convert to a base currency
                            total_value += balance.total

                    metrics["account_metrics"] = {
                        "total_assets": asset_count,
                        "total_value_estimate": total_value  # This is a rough estimate
                    }
                except Exception as e:
                    self.logger.error(f"Failed to get account metrics for bot {bot_name}: {e}")
                    metrics["account_metrics"] = {"error": str(e)}

            self.logger.debug(f"Retrieved metrics for bot {bot_name}")
            return metrics

        except Exception as e:
            self.logger.error(f"Failed to get metrics for bot {bot_name}: {e}")
            raise

    async def get_bot_logs(self, bot_name: str, lines: int = 100) -> List[str]:
        """Get recent log lines for a specific bot"""
        bot = self._get_bot(bot_name)

        try:
            # Use the bot's get_logs method from the IBot interface
            log_entries = await bot.get_logs(limit=lines)

            # Convert log entries to strings if they're not already
            logs = []
            for entry in log_entries:
                if isinstance(entry, dict):
                    # Format dictionary log entries
                    timestamp = entry.get('timestamp', '')
                    level = entry.get('level', 'INFO')
                    message = entry.get('message', '')
                    logs.append(f"[{timestamp}] {level}: {message}")
                else:
                    # Assume it's already a string
                    logs.append(str(entry))

            self.logger.debug(f"Retrieved {len(logs)} log lines for bot {bot_name}")
            return logs

        except Exception as e:
            self.logger.error(f"Failed to get logs for bot {bot_name}: {e}")
            raise

    async def update_bot_config(self, bot_name: str, config_update: Dict[str, Any]) -> None:
        """Update configuration for a specific bot"""
        bot = self._get_bot(bot_name)

        try:
            self.logger.info(f"Updating configuration for bot {bot_name}")

            # Use the bot's update_config method from the IBot interface
            await bot.update_config(config_update)

            # Also update the strategy config if strategy_params are provided
            if "strategy_params" in config_update and bot.strategy:
                try:
                    await bot.strategy.update_config(config_update["strategy_params"])
                    self.logger.info(f"Updated strategy configuration for bot {bot_name}")
                except Exception as e:
                    self.logger.error(f"Failed to update strategy config for bot {bot_name}: {e}")
                    # Don't raise here, as the bot config update might have succeeded

            self.logger.info(f"Configuration updated successfully for bot {bot_name}")

        except Exception as e:
            self.logger.error(f"Failed to update configuration for bot {bot_name}: {e}")
            raise
