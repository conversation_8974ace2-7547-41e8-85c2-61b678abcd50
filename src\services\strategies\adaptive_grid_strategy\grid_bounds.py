from decimal import Decimal
from datetime import datetime
from typing import Optional
from dataclasses import dataclass

from .trend_analysis import TrendAnalysis


@dataclass
class GridBounds:
    """Grid boundary information"""
    upper_bound: Decimal
    lower_bound: Decimal
    center_price: Decimal
    calculated_at: datetime
    confidence: Decimal = Decimal('0')
    volatility: Optional[Decimal] = None
    trend: Optional[TrendAnalysis] = None
