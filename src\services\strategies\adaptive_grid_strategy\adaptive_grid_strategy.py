"""
Adaptive Grid Trading Strategy Implementation
"""
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import asyncio
import logging
import statistics
from collections import deque

from core.interfaces import IStrategy, IBrokerage, OrderRequest, Order, MarketData, OrderSide, OrderType, Trade
from core.exceptions import StrategyError

from decimal import Decimal, ROUND_DOWN

from .circuit_breaker import CircuitBreaker
from .equity_snapshot import EquitySnapshot
from .grid_bounds import GridBounds
from .grid_level import GridLevel
from .inventory_position import InventoryPosition
from .realized_trade import RealizedTrade
from .strategy_metrics import StrategyMetrics
from .trading_fees import TradingFees
from .trend_analysis import TrendAnalysis


class AdaptiveGridStrategy(IStrategy):
    """
    Advanced adaptive grid trading strategy with dynamic bounds adjustment,
    trend analysis, and risk management features.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        # TODO: some configurations should be fetched from self._client instead of defining them manually

        # Core configuration
        self.symbol = config['symbol']
        self.base_asset = config['base_asset']
        self.quote_asset = config['quote_asset']
        self.quantity_step_size = Decimal(
            str(config.get('quantity_step_size', '1')))
        self.grid_levels = config.get('grid_levels', 20)
        self.grid_spacing = Decimal(str(config.get('grid_spacing', 0.0001)))
        # -1 means use all available
        self.total_amount = Decimal(str(config.get(
            'total_amount', -1))) if config.get('total_amount', -1) != -1 else -1

        # Adaptive features
        self.auto_adjust_bounds = config.get('auto_adjust_bounds', True)
        self.trend_analysis_enabled = config.get(
            'trend_analysis_enabled', True)
        self.dynamic_spacing = config.get('dynamic_spacing', True)
        self.volatility_adjustment = config.get('volatility_adjustment', True)

        # Risk management
        self.max_drawdown_percent = Decimal(
            str(config.get('max_drawdown_percent', 5.0)))
        self.stop_loss_percent = Decimal(
            str(config.get('stop_loss_percent', 10.0)))
        self.position_size_percent = Decimal(
            # Use 95% of available balance
            str(config.get('position_size_percent', 95.0)))

        # Timing and monitoring
        self.market_data_interval = config.get(
            'market_data_interval', 1.0)  # seconds
        self.bounds_check_interval = config.get(
            'bounds_check_interval', 300)  # 5 minutes
        self.trend_analysis_period = config.get(
            'trend_analysis_period', 100)  # number of price points
        self.price_history_limit = config.get('price_history_limit', 1000)

        # Websocket configuration
        self.use_websocket = config.get('use_websocket', True)
        self.websocket_reconnect_delay = config.get(
            'websocket_reconnect_delay', 5)

        # Internal state
        self.brokerage: Optional[IBrokerage] = None
        self.grid_levels_data: List[GridLevel] = []
        self.current_bounds: Optional[GridBounds] = None
        self.price_history: List[Tuple[datetime, Decimal]] = []
        self.active_orders: Dict[str, Order] = {}
        self.metrics = StrategyMetrics()
        self.start_time: Optional[datetime] = None

        # Inventory tracking and PnL management
        self.inventory = deque()  # FIFO queue for base asset positions
        self.realized_trades: List[RealizedTrade] = []
        self.trading_fees: Optional[TradingFees] = None

        # Risk management and equity tracking
        self.initial_capital: Optional[Decimal] = None
        self.high_water_mark: Optional[Decimal] = None
        self.equity_snapshots: List[EquitySnapshot] = []
        self.last_equity_check: Optional[datetime] = None
        self.equity_check_interval: int = config.get(
            'equity_check_interval', 60)  # seconds
        self.stop_loss_triggered: bool = False
        self.max_drawdown_triggered: bool = False

        # Async tasks
        self._market_monitor_task: Optional[asyncio.Task] = None
        self._bounds_monitor_task: Optional[asyncio.Task] = None
        self._websocket_task: Optional[asyncio.Task] = None
        self._risk_monitor_task: Optional[asyncio.Task] = None

        # State flags
        self._shutdown_requested = False
        self._websocket_connected = False

        # Logging
        self.logger = logging.getLogger(self.__class__.__name__)

        # Add circuit breakers for critical operations
        self.order_circuit_breaker = CircuitBreaker(
            failure_threshold=3, recovery_timeout=30)
        self.market_data_circuit_breaker = CircuitBreaker(
            failure_threshold=5, recovery_timeout=10)

        # Add precision settings
        self.price_precision = config.get('price_precision', 8)
        self.quantity_precision = config.get('quantity_precision', 8)
        self.min_order_size = Decimal(
            str(config.get('min_order_size', 1.00000000)))

    async def initialize(self, brokerage: IBrokerage) -> None:
        """Initialize the adaptive grid strategy"""
        try:
            self._set_brokerage(brokerage=brokerage)
            self.logger.info(
                f"Initializing Adaptive Grid Strategy for {self.symbol}")
            self.start_time = datetime.now()

            if not self.brokerage:
                raise StrategyError("Brokerage not set")

            # Validate configuration
            await self._validate_configuration()

            # Get trading fees from brokerage
            await self._initialize_trading_fees()

            # Calculate and store initial capital
            await self._initialize_capital_tracking()

            # Get initial market data and set bounds
            await self._initialize_market_data()

            # Calculate and set initial grid bounds
            await self._calculate_initial_bounds()

            # Initialize grid levels
            await self._initialize_grid_levels()

            # Start monitoring tasks
            await self._start_monitoring_tasks()

            self.is_running = True
            self.logger.info("Adaptive Grid Strategy initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize strategy: {e}")
            raise StrategyError(f"Strategy initialization failed: {e}")

    async def analyze_market(self, market_data: MarketData) -> Optional[Dict[str, Any]]:
        """
        Analyze market data and return analysis results.
        Order placement is handled internally by the grid system.
        """
        if not self.is_running:
            return None

        try:
            # Update price history
            await self._update_price_history(market_data.last_price)

            # Prepare analysis results
            analysis = {
                'current_price': Decimal(str(market_data.last_price)),
                'price_change': None,
                'trend': None,
                'volatility': None,
                'grid_status': {
                    'active_orders': len(self.active_orders),
                    'bounds_valid': self._is_price_within_bounds(market_data.last_price)
                }
            }

            # Calculate price change if we have history
            if len(self.price_history) > 1:
                prev_price = Decimal(str(self.price_history[-2][1]))
                current_price = Decimal(str(market_data.last_price))
                analysis['price_change'] = (
                    (current_price - prev_price) / prev_price * 100)

            # Get trend analysis
            if self.trend_analysis_enabled:
                analysis['trend'] = await self._analyze_trend()

            # Check if we need to adjust grid bounds
            if await self._should_adjust_bounds(market_data):
                await self._adjust_grid_bounds(market_data)
                analysis['grid_status']['bounds_adjusted'] = True

            # Perform risk monitoring (stop-loss and max drawdown checks)
            await self._monitor_risk_levels(market_data)

            return analysis

        except Exception as e:
            self.logger.error(f"Error in market analysis: {e}")
            return None

    def _is_price_within_bounds(self, price: float) -> bool:
        """Check if price is within current grid bounds"""
        if not self.current_bounds:
            return False

        price_decimal = Decimal(str(price))
        return self.current_bounds.lower_bound <= price_decimal <= self.current_bounds.upper_bound

    async def on_order_filled(self, order: Order) -> None:
        """Handle order fill events"""
        try:
            self.logger.info(
                f"Order filled: {order.id} - {order.side} {order.quantity} @ {order.price}")

            # Calculate fees for this order
            fees = await self._calculate_order_fees(order)

            # Update inventory and calculate PnL
            await self._update_inventory_and_pnl(order, fees)

            # Update metrics with proper PnL calculation
            await self._update_trade_metrics_with_fees(order, fees)

            # Remove from active orders
            if order.id in self.active_orders:
                del self.active_orders[order.id]

            # Update grid level
            await self._update_grid_level_after_fill(order)

            # Place opposite order if conditions are met
            await self._place_opposite_order(order)

        except Exception as e:
            self.logger.error(f"Error handling order fill: {e}")

    async def on_order_cancelled(self, order: Order) -> None:
        """Handle order cancellation events"""
        try:
            self.logger.info(f"Order cancelled: {order.id}")

            # Remove from active orders
            if order.id in self.active_orders:
                del self.active_orders[order.id]

            # Update grid level
            await self._update_grid_level_after_cancel(order)

        except Exception as e:
            self.logger.error(f"Error handling order cancellation: {e}")

    async def shutdown(self) -> None:
        """Shutdown the strategy"""
        try:
            self.logger.info("Shutting down Adaptive Grid Strategy")
            self._shutdown_requested = True
            self.is_running = False

            # Cancel all monitoring tasks
            await self._stop_monitoring_tasks()

            # Cancel all open orders
            await self._cancel_all_orders()

            # Calculate final metrics
            await self._calculate_final_metrics()

            self.logger.info("Adaptive Grid Strategy shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during strategy shutdown: {e}")

    def _set_brokerage(self, brokerage: IBrokerage) -> None:
        """Set the brokerage instance"""
        self.brokerage = brokerage

    async def _validate_configuration(self) -> None:
        """Enhanced configuration validation"""
        if self.grid_levels < 2:
            raise StrategyError("Grid levels must be at least 2")

        if self.grid_levels > 100:
            raise StrategyError(
                "Grid levels cannot exceed 100 for performance reasons")

        if self.grid_spacing <= 0:
            raise StrategyError("Grid spacing must be positive")

        if not self.symbol or not self.base_asset or not self.quote_asset:
            raise StrategyError("Symbol and asset names must be specified")

        if not hasattr(self, 'quantity_step_size') or self.quantity_step_size <= Decimal('0'):
            raise StrategyError(
                "quantity_step_size must be positive and defined.")

        # Validate symbol format
        expected_symbol = f"{self.base_asset}{self.quote_asset}"
        if self.symbol.upper() != expected_symbol.upper():
            self.logger.warning(
                f"Symbol {self.symbol} doesn't match expected format {expected_symbol}")

        # Enhanced balance validation with minimum requirements
        try:
            balances = await self.brokerage.get_account_balance()
            base_balance = next(
                (b for b in balances if b.asset == self.base_asset), None)
            quote_balance = next(
                (b for b in balances if b.asset == self.quote_asset), None)

            if not base_balance and not quote_balance:
                raise StrategyError(
                    f"No balance found for {self.base_asset} or {self.quote_asset}")

            # Check minimum balance requirements
            min_balance_required = self.grid_levels * self.min_order_size
            total_balance_value = 0

            if base_balance:
                current_price = self.price_history[-1][1] if self.price_history else await self._get_current_price()
                total_balance_value += base_balance.total * current_price

            if quote_balance:
                total_balance_value += quote_balance.total

            if total_balance_value < min_balance_required:
                raise StrategyError(
                    f"Insufficient balance. Required: {min_balance_required}, Available: {total_balance_value}")

        except Exception as e:
            if isinstance(e, StrategyError):
                raise
            raise StrategyError(f"Balance validation failed: {e}")

    async def _get_current_price(self) -> float:
        """Get current market price"""
        try:
            market_data = await self.market_data_circuit_breaker.call(self.brokerage.get_market_data, self.symbol)
            return market_data.last_price
        except Exception as e:
            self.logger.error(f"Failed to get current price: {e}")
            return self.price_history[-1][1] if self.price_history else 0.0

    async def _initialize_market_data(self) -> None:
        """Initialize market data and price history"""
        # Get current market data
        market_data = await self.brokerage.get_market_data(self.symbol)
        current_price = Decimal(str(market_data.last_price))

        # Initialize price history with current price
        self.price_history = [(datetime.now(), current_price)]

        self.logger.info(
            f"Initial market price for {self.symbol}: {current_price}")

    async def _calculate_initial_bounds(self) -> None:
        """Calculate initial grid bounds based on current market conditions"""
        if not self.price_history:
            raise StrategyError(
                "No price history available for bounds calculation")

        current_price = self.price_history[-1][1]  # Already Decimal

        # Calculate bounds based on grid spacing and levels
        total_grid_range = self.grid_spacing * \
            Decimal(str(self.grid_levels - 1))
        center_offset = total_grid_range / Decimal('2')

        upper_bound = current_price + center_offset
        lower_bound = current_price - center_offset

        self.current_bounds = GridBounds(
            upper_bound=self._round_price(upper_bound),
            lower_bound=self._round_price(lower_bound),
            center_price=current_price,
            calculated_at=datetime.now(),
            confidence=Decimal('1.0')
        )

        self.logger.info(
            f"Initial grid bounds: {lower_bound:.4f} - {upper_bound:.4f}")

    async def _initialize_grid_levels(self) -> None:
        """Initialize grid levels: define prices, then calculate quantities, then place orders."""
        if not self.current_bounds:
            raise StrategyError("Grid bounds not set")

        # --- Stage 1: Define grid level prices and create GridLevel objects ---
        self.grid_levels_data = []  # Ensure it's reset if re-initializing

        # Ensure price_step calculation uses Decimals correctly
        if self.grid_levels <= 1:  # Avoid division by zero or negative if grid_levels is 1 or less
            if self.grid_levels == 1:
                price_step = Decimal('0')  # Or handle as a special case
            else:
                raise StrategyError(
                    "grid_levels must be greater than 1 to calculate price_step.")
        else:
            price_step = (self.current_bounds.upper_bound -
                          self.current_bounds.lower_bound) / Decimal(str(self.grid_levels - 1))

        for i in range(self.grid_levels):
            price = self.current_bounds.lower_bound + (i * price_step)
            # Create GridLevel objects with their price. Quantities will be added later.
            # Make sure to round the price to the defined precision
            grid_level = GridLevel(price=self._round_price(price))
            self.grid_levels_data.append(grid_level)

        self.logger.debug(
            f"Defined {len(self.grid_levels_data)} grid levels with prices.")

        # At this point, self.grid_levels_data is populated with GridLevel objects,
        # each having its 'price' attribute set.

        # --- Stage 2: Calculate available balances ---
        base_balance, quote_balance = await self._get_available_balances()

        # --- Stage 3: Calculate order quantities for each level ---
        # _calculate_level_quantities will now use the populated self.grid_levels_data
        buy_quantity_per_level, sell_quantity_per_level = await self._calculate_level_quantities(
            base_balance, quote_balance
        )

        self.logger.debug(
            f"Calculated quantities: Buy per level: {buy_quantity_per_level}, Sell per level: {sell_quantity_per_level}")

        # --- Stage 4: Assign calculated quantities to the GridLevel objects ---
        for level in self.grid_levels_data:
            # Make sure to round quantities
            level.buy_quantity = self._round_quantity(buy_quantity_per_level)
            level.sell_quantity = self._round_quantity(sell_quantity_per_level)

        # --- Stage 5: Place initial orders ---
        await self._place_initial_grid_orders()

        self.logger.info(
            f"Initialized {len(self.grid_levels_data)} grid levels and placed initial orders.")

    async def _find_nearest_grid_level(self, price: Decimal) -> int:
        """Find the nearest grid level index for a given price"""
        if not self.grid_levels_data:
            return 0

        min_diff = float('inf')
        nearest_index = 0

        for i, level in enumerate(self.grid_levels_data):
            diff = abs(level.price - price)
            if diff < min_diff:
                min_diff = diff
                nearest_index = i

        return nearest_index

    async def _place_initial_grid_orders(self) -> None:
        """Place initial grid orders"""
        current_price = self.price_history[-1][1]  # Already Decimal

        for i, level in enumerate(self.grid_levels_data):
            try:
                # Place buy order if level price is below current price
                if level.price < current_price and level.buy_quantity > 0:
                    buy_order = await self._place_grid_order(
                        side=OrderSide.BUY,
                        price=level.price,
                        quantity=level.buy_quantity
                    )
                    if buy_order:
                        level.buy_order_id = buy_order.id
                        self.active_orders[buy_order.id] = buy_order

                # Place sell order if level price is above current price
                elif level.price > current_price and level.sell_quantity > 0:
                    sell_order = await self._place_grid_order(
                        side=OrderSide.SELL,
                        price=level.price,
                        quantity=level.sell_quantity
                    )
                    if sell_order:
                        level.sell_order_id = sell_order.id
                        self.active_orders[sell_order.id] = sell_order

            except Exception as e:
                self.logger.error(
                    f"Failed to place initial order at level {i}: {e}")

    async def _place_grid_order(self, side: OrderSide, price: Decimal, quantity: Decimal) -> Optional[Order]:
        """Place a grid order"""
        try:
            if quantity <= Decimal('0'):
                return None

            # Round quantity to exchange precision
            rounded_quantity = self._round_quantity(quantity)
            rounded_price = self._round_price(price)

            order_request = OrderRequest(
                symbol=self.symbol,
                side=side,
                order_type=OrderType.LIMIT,
                quantity=float(rounded_quantity),
                price=float(rounded_price),
                time_in_force='GTC'
            )

            # Use circuit breaker for order placement
            order = await self.order_circuit_breaker.call(self.brokerage.place_order, order_request)
            self.logger.debug(
                f"Placed {side.value} order: {rounded_quantity} @ {rounded_price}")
            return order

        except Exception as e:
            # TODO: make sure that the bot error_property status is increased
            self.logger.error(
                f"Failed to place {side.value} order at {price}: {e}")
            return None

    async def _start_monitoring_tasks(self) -> None:
        """Start background monitoring tasks"""
        if self.use_websocket:
            self._websocket_task = asyncio.create_task(
                self._websocket_price_monitor())
        else:
            # Start fallback monitoring if websocket disabled
            self._market_monitor_task = asyncio.create_task(
                self._fallback_price_monitor())

        self._bounds_monitor_task = asyncio.create_task(
            self._bounds_monitor_loop())

        self._risk_monitor_task = asyncio.create_task(
            self._risk_monitor_loop())

    async def _stop_monitoring_tasks(self) -> None:
        """Stop background monitoring tasks"""
        tasks = [self._market_monitor_task,
                 self._bounds_monitor_task, self._websocket_task, self._risk_monitor_task]

        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    async def _websocket_price_monitor(self) -> None:
        """Monitor price updates via websocket with proper error handling and reconnection"""
        while not self._shutdown_requested:
            try:
                if hasattr(self.brokerage, 'subscribe_to_price_updates'):
                    # Use brokerage's websocket if available
                    async for market_data in self.brokerage.subscribe_to_price_updates(self.symbol):
                        if self._shutdown_requested:
                            break

                        await self._update_price_history(market_data.last_price)
                        self._websocket_connected = True

                        # Process the market data through our analysis
                        await self.analyze_market(market_data)
                else:
                    # Fallback to polling if websocket not available
                    self.logger.warning(
                        "WebSocket not available, falling back to polling")
                    await self._fallback_price_monitor()
                    break

            except Exception as e:
                self.logger.error(f"WebSocket error: {e}")
                self._websocket_connected = False
                await asyncio.sleep(self.websocket_reconnect_delay)

    async def _fallback_price_monitor(self) -> None:
        """Fallback price monitoring when WebSocket is unavailable"""
        while not self._shutdown_requested:
            try:
                market_data = await self.market_data_circuit_breaker.call(
                    self.brokerage.get_market_data, self.symbol
                )
                await self._update_price_history(market_data.last_price)
                await self.analyze_market(market_data)
                await asyncio.sleep(1.0)  # 1 second polling
            except Exception as e:
                self.logger.error(f"Fallback price monitor error: {e}")
                await asyncio.sleep(5)

    async def _bounds_monitor_loop(self) -> None:
        """Monitor and adjust grid bounds periodically"""
        while not self._shutdown_requested:
            try:
                if self.auto_adjust_bounds:
                    market_data = await self.brokerage.get_market_data(self.symbol)

                    if await self._should_adjust_bounds(market_data):
                        await self._adjust_grid_bounds(market_data)

                await asyncio.sleep(self.bounds_check_interval)

            except Exception as e:
                self.logger.error(f"Bounds monitor error: {e}")
                await asyncio.sleep(30)

    async def _update_price_history(self, price: float) -> None:
        """Update price history with new price point"""
        now = datetime.now()
        price_decimal = Decimal(str(price))
        self.price_history.append((now, price_decimal))

        # Keep history within limits
        if len(self.price_history) > self.price_history_limit:
            self.price_history = self.price_history[-self.price_history_limit:]

    async def _should_adjust_bounds(self, market_data: MarketData) -> bool:
        """Determine if grid bounds should be adjusted"""
        if not self.current_bounds or not self.price_history:
            return False

        current_price = market_data.last_price

        # Check if price is outside current bounds
        if current_price <= self.current_bounds.lower_bound or current_price >= self.current_bounds.upper_bound:
            return True

        # Check if bounds are too old
        bounds_age = datetime.now() - self.current_bounds.calculated_at
        if bounds_age > timedelta(hours=1):
            return True

        # Check trend analysis
        if self.trend_analysis_enabled:
            trend = await self._analyze_trend()
            if trend in [TrendAnalysis.STRONG_BULLISH, TrendAnalysis.STRONG_BEARISH]:
                return True

        return False

    async def _analyze_trend(self) -> TrendAnalysis:
        """Enhanced trend analysis with multiple indicators"""
        if len(self.price_history) < self.trend_analysis_period:
            return TrendAnalysis.NEUTRAL

        recent_prices = [
            float(p[1]) for p in self.price_history[-self.trend_analysis_period:]]

        # Simple Moving Average trend
        sma_short = statistics.mean(recent_prices[-10:])
        sma_long = statistics.mean(
            recent_prices[-30:]) if len(recent_prices) >= 30 else statistics.mean(recent_prices)

        # Price momentum
        price_change = (recent_prices[-1] -
                        recent_prices[0]) / recent_prices[0] * 100

        # Volatility
        volatility = statistics.stdev(
            recent_prices) / statistics.mean(recent_prices) * 100

        # RSI-like momentum indicator
        gains = []
        losses = []
        for i in range(1, len(recent_prices)):
            change = recent_prices[i] - recent_prices[i-1]
            if change > 0:
                gains.append(change)
            else:
                losses.append(abs(change))

        avg_gain = statistics.mean(gains) if gains else 0
        avg_loss = statistics.mean(losses) if losses else 0

        # Combine indicators
        trend_score = 0

        # SMA trend component
        if sma_short > sma_long * 1.001:
            trend_score += 1
        elif sma_short < sma_long * 0.999:
            trend_score -= 1

        # Price momentum component
        if price_change > 1.0:
            trend_score += 2
        elif price_change > 0.3:
            trend_score += 1
        elif price_change < -1.0:
            trend_score -= 2
        elif price_change < -0.3:
            trend_score -= 1

        # Momentum strength component
        if avg_gain > avg_loss * 2:
            trend_score += 1
        elif avg_loss > avg_gain * 2:
            trend_score -= 1

        # Classify trend based on score and volatility
        if trend_score >= 3 and volatility < 2:
            return TrendAnalysis.STRONG_BULLISH
        elif trend_score >= 2:
            return TrendAnalysis.BULLISH
        elif trend_score <= -3 and volatility < 2:
            return TrendAnalysis.STRONG_BEARISH
        elif trend_score <= -2:
            return TrendAnalysis.BEARISH
        else:
            return TrendAnalysis.NEUTRAL

    async def _adjust_grid_bounds(self, market_data: MarketData) -> None:
        """Adjust grid bounds based on market conditions"""
        try:
            self.logger.info("Adjusting grid bounds...")

            # Cancel all existing orders
            await self._cancel_all_grid_orders()

            # Calculate new bounds
            await self._calculate_adaptive_bounds(market_data)

            # Reinitialize grid levels with new bounds
            await self._reinitialize_grid_levels()

            # Update metrics
            self.metrics.grid_resets += 1
            self.metrics.last_reset = datetime.now()

            self.logger.info(
                f"Grid bounds adjusted to: {self.current_bounds.lower_bound:.4f} - {self.current_bounds.upper_bound:.4f}")

        except Exception as e:
            self.logger.error(f"Failed to adjust grid bounds: {e}")

    async def _calculate_adaptive_bounds(self, market_data: MarketData) -> None:
        """Enhanced bounds calculation with multiple factors"""
        current_price = Decimal(str(market_data.last_price))

        # Calculate volatility over different periods
        volatility_1h = await self._calculate_volatility(timedelta(hours=1))
        volatility_24h = await self._calculate_volatility(timedelta(hours=24))

        # Use weighted volatility
        volatility = (volatility_1h * 0.7 + volatility_24h * 0.3)

        # Calculate dynamic spacing based on volatility
        if self.dynamic_spacing:
            base_spacing = self.grid_spacing
            volatility_multiplier = max(
                0.5, min(2.0, 1 + (volatility - 0.01) * 10))
            spacing = base_spacing * Decimal(str(volatility_multiplier))
        else:
            spacing = self.grid_spacing

        # Calculate range based on grid levels and spacing
        total_range = spacing * (self.grid_levels - 1)

        # Get trend analysis
        trend = await self._analyze_trend() if self.trend_analysis_enabled else TrendAnalysis.NEUTRAL

        # Adjust bounds based on trend with more sophisticated logic
        if trend == TrendAnalysis.STRONG_BULLISH:
            # Bias towards higher prices
            lower_offset = total_range * Decimal('0.2')
            upper_offset = total_range * Decimal('0.8')
        elif trend == TrendAnalysis.STRONG_BEARISH:
            # Bias towards lower prices
            lower_offset = total_range * Decimal('0.8')
            upper_offset = total_range * Decimal('0.2')
        elif trend in [TrendAnalysis.BULLISH, TrendAnalysis.BEARISH]:
            # Slight bias
            bias = Decimal(
                '0.6') if trend == TrendAnalysis.BULLISH else Decimal('0.4')
            lower_offset = total_range * (1 - bias)
            upper_offset = total_range * bias
        else:
            # Neutral - center around current price
            lower_offset = upper_offset = total_range / 2

        lower_bound = current_price - lower_offset
        upper_bound = current_price + upper_offset

        # Ensure bounds don't go negative
        lower_bound = max(lower_bound, current_price * Decimal('0.1'))

        # Round to appropriate precision
        lower_bound = self._round_price(lower_bound)
        upper_bound = self._round_price(upper_bound)

        self.current_bounds = GridBounds(
            upper_bound=self._round_price(upper_bound),
            lower_bound=self._round_price(lower_bound),
            center_price=current_price,
            calculated_at=datetime.now(),
            confidence=Decimal('0.8'),
            volatility=Decimal(str(volatility)),
            trend=trend
        )

        self.last_bounds_update = datetime.now()

    async def _calculate_volatility(self, period: timedelta) -> float:
        """Calculate price volatility over specified period"""
        if not self.price_history:
            return 0.0

        cutoff_time = datetime.now() - period
        relevant_prices = [
            float(p[1]) for p in self.price_history
            if p[0] >= cutoff_time
        ]

        if len(relevant_prices) < 2:
            return 0.0

        returns = []
        for i in range(1, len(relevant_prices)):
            ret = (relevant_prices[i] -
                   relevant_prices[i-1]) / relevant_prices[i-1]
            returns.append(ret)

        if not returns:
            return 0.0

        return statistics.stdev(returns) if len(returns) > 1 else 0.0

    def _round_price(self, price: Decimal) -> Decimal:
        """Round price to exchange precision"""
        precision_str = '0.' + '0' * (self.price_precision - 1) + '1'
        precision = Decimal(precision_str)
        return price.quantize(precision, rounding=ROUND_DOWN)

    def _round_quantity(self, quantity: Decimal) -> Decimal:
        """Round quantity down to the nearest multiple of self.quantity_step_size."""
        if self.quantity_step_size <= Decimal('0'):
            # Avoid division by zero or invalid step size, return quantity as is or raise error
            self.logger.warning(
                "quantity_step_size is zero or negative. Returning unrounded quantity.")
            return quantity

        # Ensure the quantity is a multiple of the step size by rounding down
        # (quantity // self.quantity_step_size) gives how many "steps" fit into the quantity
        # Multiplying by self.quantity_step_size gives the rounded quantity
        return (quantity // self.quantity_step_size) * self.quantity_step_size

    async def _calculate_dynamic_spacing(self) -> float:
        """Calculate dynamic spacing based on volatility"""
        if len(self.price_history) < 20:
            return self.grid_spacing

        recent_prices = [p[1] for p in self.price_history[-20:]]
        volatility = statistics.stdev(
            recent_prices) / statistics.mean(recent_prices)

        # Adjust spacing based on volatility
        if volatility > 0.01:  # High volatility
            return self.grid_spacing * 1.5
        elif volatility < 0.005:  # Low volatility
            return self.grid_spacing * 0.7
        else:
            return self.grid_spacing

    async def _reinitialize_grid_levels(self) -> None:
        """Reinitialize grid levels with new bounds"""
        # Clear old grid levels
        self.grid_levels_data.clear()

        # Initialize new grid levels
        await self._initialize_grid_levels()

    async def _cancel_all_grid_orders(self) -> None:
        """Cancel all active grid orders"""
        for order_id, order in list(self.active_orders.items()):
            try:
                success = await self.brokerage.cancel_order(order_id, self.symbol)
                if success:
                    del self.active_orders[order_id]
                    await self._update_grid_level_after_cancel(order)
            except Exception as e:
                self.logger.error(f"Failed to cancel order {order_id}: {e}")

    async def _analyze_grid_opportunities(self, market_data: MarketData) -> Optional[OrderRequest]:
        """Analyze grid for new order opportunities"""
        # This method can return additional orders if needed
        # For now, the grid operates through the order fill mechanism
        return None

    async def _update_grid_level_after_fill(self, order: Order) -> None:
        """Update grid level after order fill"""
        for level in self.grid_levels_data:
            if (order.side == OrderSide.BUY and level.buy_order_id == order.id) or \
               (order.side == OrderSide.SELL and level.sell_order_id == order.id):

                level.last_filled = datetime.now()

                if order.side == OrderSide.BUY:
                    level.buy_order_id = None
                else:
                    level.sell_order_id = None

                break

    async def _update_grid_level_after_cancel(self, order: Order) -> None:
        """Update grid level after order cancellation"""
        for level in self.grid_levels_data:
            if (order.side == OrderSide.BUY and level.buy_order_id == order.id) or \
               (order.side == OrderSide.SELL and level.sell_order_id == order.id):

                if order.side == OrderSide.BUY:
                    level.buy_order_id = None
                else:
                    level.sell_order_id = None

                break

    async def on_order_rejected(self, order: Order) -> None:
        """Handle order rejection events"""
        try:
            self.logger.warning(
                f"Order rejected: {order.id} - {order.side} {order.quantity} @ {order.price}")

            # Remove from active orders if present
            if order.id in self.active_orders:
                del self.active_orders[order.id]

            # Update grid level to clear the rejected order
            await self._update_grid_level_after_cancel(order)

            # Attempt to replace the rejected order with adjusted parameters
            await self._handle_rejected_order_replacement(order)

        except Exception as e:
            self.logger.error(f"Error handling order rejection: {e}")

    async def on_trade_executed(self, trade: Trade) -> None:
        """Handle trade execution events"""
        try:
            self.logger.info(
                f"Trade executed: {trade.id} - {trade.side} {trade.quantity} @ {trade.price}")

            # Update metrics with trade data
            self.metrics.total_trades += 1
            self.metrics.total_volume += trade.quantity

            # Calculate and update profit
            if trade.side == OrderSide.SELL:
                # For sell trades, we made profit (simplified calculation)
                profit = trade.quantity * trade.price * 0.0001  # Small spread assumption
                self.metrics.total_profit += profit
                if profit > 0:
                    self.metrics.profitable_trades += 1

            # Update average profit per trade
            if self.metrics.total_trades > 0:
                self.metrics.avg_profit_per_trade = self.metrics.total_profit / \
                    self.metrics.total_trades

        except Exception as e:
            self.logger.error(f"Error handling trade execution: {e}")

    async def get_strategy_status(self) -> Dict[str, Any]:
        """Get current strategy status and metrics"""
        try:
            # Calculate uptime
            uptime = datetime.now() - self.start_time if self.start_time else timedelta()

            # Count active orders by type
            active_buy_orders = sum(
                1 for order in self.active_orders.values() if order.side == OrderSide.BUY)
            active_sell_orders = sum(
                1 for order in self.active_orders.values() if order.side == OrderSide.SELL)

            # Get current market price
            current_price = self.price_history[-1][1] if self.price_history else 0.0

            status = {
                "strategy_name": "AdaptiveGridStrategy",
                "symbol": self.symbol,
                "is_running": self.is_running,
                "uptime_seconds": uptime.total_seconds(),
                "current_price": current_price,
                "grid_levels": len(self.grid_levels_data),
                "active_orders": len(self.active_orders),
                "active_buy_orders": active_buy_orders,
                "active_sell_orders": active_sell_orders,
                "grid_bounds": {
                    "upper": self.current_bounds.upper_bound if self.current_bounds else None,
                    "lower": self.current_bounds.lower_bound if self.current_bounds else None,
                    "center": self.current_bounds.center_price if self.current_bounds else None,
                    "last_calculated": self.current_bounds.calculated_at.isoformat() if self.current_bounds else None
                },
                "configuration": {
                    "grid_levels": self.grid_levels,
                    "grid_spacing": self.grid_spacing,
                    "auto_adjust_bounds": self.auto_adjust_bounds,
                    "trend_analysis_enabled": self.trend_analysis_enabled,
                    "dynamic_spacing": self.dynamic_spacing,
                    "max_drawdown_percent": self.max_drawdown_percent
                },
                "metrics": {
                    "total_trades": self.metrics.total_trades,
                    "profitable_trades": self.metrics.profitable_trades,
                    "total_profit": self.metrics.total_profit,
                    "total_volume": self.metrics.total_volume,
                    "avg_profit_per_trade": self.metrics.avg_profit_per_trade,
                    "grid_resets": self.metrics.grid_resets,
                    "last_reset": self.metrics.last_reset.isoformat() if self.metrics.last_reset else None
                }
            }

            return status

        except Exception as e:
            self.logger.error(f"Error getting strategy status: {e}")
            return {"error": str(e)}

    async def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update strategy configuration"""
        try:
            self.logger.info("Updating strategy configuration...")

            # Store old config for rollback if needed
            old_config = self.config.copy()

            # Update configuration parameters
            for key, value in new_config.items():
                if hasattr(self, key):
                    setattr(self, key, value)
                    self.config[key] = value

            # Validate the new configuration
            await self._validate_configuration()

            # If bounds adjustment parameters changed, recalculate bounds
            if any(key in new_config for key in ['grid_levels', 'grid_spacing', 'auto_adjust_bounds']):
                if self.is_running:
                    market_data = await self.brokerage.get_market_data(self.symbol)
                    await self._adjust_grid_bounds(market_data)

            self.logger.info("Strategy configuration updated successfully")

        except Exception as e:
            self.logger.error(f"Failed to update configuration: {e}")
            # Rollback configuration
            self.config = old_config
            raise StrategyError(f"Configuration update failed: {e}")

    async def get_positions(self) -> List[Dict[str, Any]]:
        """Get current positions managed by the strategy"""
        try:
            positions = []

            # Get account balances
            balances = await self.brokerage.get_account_balance()
            base_balance = next(
                (b for b in balances if b.asset == self.base_asset), None)
            quote_balance = next(
                (b for b in balances if b.asset == self.quote_asset), None)

            # Calculate current position value
            current_price = self.price_history[-1][1] if self.price_history else 0.0

            if base_balance:
                positions.append({
                    "asset": self.base_asset,
                    "free": base_balance.free,
                    "locked": base_balance.locked,
                    "total": base_balance.total,
                    "value_in_quote": base_balance.total * current_price
                })

            if quote_balance:
                positions.append({
                    "asset": self.quote_asset,
                    "free": quote_balance.free,
                    "locked": quote_balance.locked,
                    "total": quote_balance.total,
                    "value_in_quote": quote_balance.total
                })

            # Add grid level information
            grid_info = {
                "asset": "GRID_POSITIONS",
                "active_buy_orders": sum(1 for level in self.grid_levels_data if level.buy_order_id),
                "active_sell_orders": sum(1 for level in self.grid_levels_data if level.sell_order_id),
                "total_grid_levels": len(self.grid_levels_data),
                "grid_utilization": len(self.active_orders) / (self.grid_levels * 2) if self.grid_levels > 0 else 0
            }
            positions.append(grid_info)

            return positions

        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []

    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        try:
            # Calculate additional performance metrics
            uptime = datetime.now() - self.start_time if self.start_time else timedelta()
            win_rate = (self.metrics.profitable_trades / self.metrics.total_trades *
                        100) if self.metrics.total_trades > 0 else 0.0

            # Calculate daily/hourly rates
            uptime_hours = uptime.total_seconds() / 3600
            trades_per_hour = self.metrics.total_trades / \
                uptime_hours if uptime_hours > 0 else 0.0
            profit_per_hour = self.metrics.total_profit / \
                uptime_hours if uptime_hours > 0 else 0.0

            # Get current market info
            current_price = self.price_history[-1][1] if self.price_history else 0.0
            price_volatility = 0.0
            if len(self.price_history) > 1:
                # Last 50 price points
                recent_prices = [p[1] for p in self.price_history[-50:]]
                if len(recent_prices) > 1:
                    price_volatility = statistics.stdev(
                        recent_prices) / statistics.mean(recent_prices) * 100

            metrics = {
                "basic_metrics": {
                    "total_trades": self.metrics.total_trades,
                    "profitable_trades": self.metrics.profitable_trades,
                    "losing_trades": self.metrics.total_trades - self.metrics.profitable_trades,
                    "win_rate_percent": win_rate,
                    "total_profit": self.metrics.total_profit,
                    "total_volume": self.metrics.total_volume,
                    "avg_profit_per_trade": self.metrics.avg_profit_per_trade
                },
                "time_based_metrics": {
                    "uptime_hours": uptime_hours,
                    "trades_per_hour": trades_per_hour,
                    "profit_per_hour": profit_per_hour,
                    "grid_resets": self.metrics.grid_resets,
                    "last_reset": self.metrics.last_reset.isoformat() if self.metrics.last_reset else None
                },
                "market_metrics": {
                    "current_price": current_price,
                    "price_volatility_percent": price_volatility,
                    "price_history_points": len(self.price_history),
                    "grid_efficiency": len(self.active_orders) / (self.grid_levels * 2) if self.grid_levels > 0 else 0
                },
                "risk_metrics": {
                    "max_drawdown_limit": self.max_drawdown_percent,
                    "stop_loss_limit": self.stop_loss_percent,
                    "position_utilization": self.position_size_percent
                }
            }

            return metrics

        except Exception as e:
            self.logger.error(f"Error calculating performance metrics: {e}")
            return {"error": str(e)}

    # Missing helper methods

    async def _find_higher_grid_level(self, current_level: GridLevel) -> Optional[GridLevel]:
        """Find the next higher grid level"""
        current_index = self.grid_levels_data.index(current_level)
        if current_index < len(self.grid_levels_data) - 1:
            return self.grid_levels_data[current_index + 1]
        return None

    async def _find_lower_grid_level(self, current_level: GridLevel) -> Optional[GridLevel]:
        """Find the next lower grid level"""
        current_index = self.grid_levels_data.index(current_level)
        if current_index > 0:
            return self.grid_levels_data[current_index - 1]
        return None

    async def _handle_rejected_order_replacement(self, rejected_order: Order) -> None:
        """Handle replacement of rejected orders"""
        try:
            # Find the grid level for the rejected order
            for level in self.grid_levels_data:
                if abs(level.price - rejected_order.price) < self.grid_spacing / 2:
                    # Adjust price slightly and retry
                    if rejected_order.side == OrderSide.BUY:
                        # Lower the buy price slightly
                        new_price = rejected_order.price * 0.999
                        new_order = await self._place_grid_order(
                            side=OrderSide.BUY,
                            price=new_price,
                            quantity=rejected_order.quantity
                        )
                        if new_order:
                            level.buy_order_id = new_order.id
                            self.active_orders[new_order.id] = new_order
                    else:
                        # Raise the sell price slightly
                        new_price = rejected_order.price * 1.001
                        new_order = await self._place_grid_order(
                            side=OrderSide.SELL,
                            price=new_price,
                            quantity=rejected_order.quantity
                        )
                        if new_order:
                            level.sell_order_id = new_order.id
                            self.active_orders[new_order.id] = new_order
                    break

        except Exception as e:
            self.logger.error(f"Failed to replace rejected order: {e}")

    async def _cancel_all_orders(self) -> None:
        """Cancel all active orders during shutdown"""
        for order_id in list(self.active_orders.keys()):
            try:
                success = await self.brokerage.cancel_order(order_id, self.symbol)
                if success:
                    del self.active_orders[order_id]
                    self.logger.info(f"Cancelled order: {order_id}")
            except Exception as e:
                self.logger.error(f"Failed to cancel order {order_id}: {e}")

    async def _calculate_final_metrics(self) -> None:
        """Calculate final metrics during shutdown"""
        if self.start_time:
            self.metrics.uptime = datetime.now() - self.start_time

        self.logger.info(f"Final Strategy Metrics:")
        self.logger.info(f"Total Trades: {self.metrics.total_trades}")
        self.logger.info(
            f"Profitable Trades: {self.metrics.profitable_trades}")
        self.logger.info(f"Total Profit: {self.metrics.total_profit}")
        self.logger.info(
            f"Average Profit per Trade: {self.metrics.avg_profit_per_trade}")
        self.logger.info(f"Grid Resets: {self.metrics.grid_resets}")
        self.logger.info(f"Uptime: {self.metrics.uptime}")

    # ============================================================================
    # FEE MANAGEMENT METHODS
    # ============================================================================

    async def _initialize_trading_fees(self) -> None:
        """Initialize trading fees from brokerage"""
        try:
            # Try to get trading fees from brokerage
            if hasattr(self.brokerage, 'get_trading_fees'):
                fees_info = await self.brokerage.get_trading_fees(self.symbol)
                self.trading_fees = TradingFees(
                    maker_fee_rate=Decimal(
                        str(fees_info.get('maker_fee', 0.001))),
                    taker_fee_rate=Decimal(
                        str(fees_info.get('taker_fee', 0.001))),
                    fee_asset=fees_info.get('fee_asset', self.quote_asset)
                )
            else:
                # Use default fees if brokerage doesn't provide fee info
                self.trading_fees = TradingFees(
                    maker_fee_rate=Decimal('0.001'),  # 0.1% default
                    taker_fee_rate=Decimal('0.001'),  # 0.1% default
                    fee_asset=self.quote_asset
                )
                self.logger.warning(
                    "Using default trading fees - brokerage doesn't provide fee information")

            self.logger.info(f"Trading fees initialized: Maker {self.trading_fees.maker_fee_rate}, "
                             f"Taker {self.trading_fees.taker_fee_rate}")

        except Exception as e:
            self.logger.error(f"Failed to initialize trading fees: {e}")
            # Use conservative default fees
            self.trading_fees = TradingFees(
                maker_fee_rate=Decimal('0.001'),
                taker_fee_rate=Decimal('0.001'),
                fee_asset=self.quote_asset
            )

    async def _calculate_order_fees(self, order: Order) -> Decimal:
        """Calculate fees for an order"""
        try:
            if not self.trading_fees:
                await self._initialize_trading_fees()

            # For grid trading, we typically use limit orders (maker fees)
            fee_rate = self.trading_fees.maker_fee_rate

            # Calculate fee based on order value
            order_value = Decimal(str(order.filled_quantity)
                                  ) * Decimal(str(order.price))
            fees = order_value * fee_rate

            return fees

        except Exception as e:
            self.logger.error(f"Error calculating order fees: {e}")
            # Return conservative estimate
            order_value = Decimal(str(order.filled_quantity)
                                  ) * Decimal(str(order.price))
            return order_value * Decimal('0.001')  # 0.1% default

    # ============================================================================
    # INVENTORY TRACKING AND PNL CALCULATION METHODS
    # ============================================================================

    async def _update_inventory_and_pnl(self, order: Order, fees: Decimal) -> None:
        """Update inventory and calculate PnL based on order fill"""
        try:
            quantity = Decimal(str(order.filled_quantity))
            price = Decimal(str(order.price))

            if order.side == OrderSide.BUY:
                # Add to inventory
                position = InventoryPosition(
                    quantity=quantity,
                    cost_basis=price,
                    timestamp=datetime.now(),
                    fees_paid=fees,
                    order_id=order.id
                )
                self.inventory.append(position)
                self.logger.debug(f"Added to inventory: {quantity} @ {price}")

            elif order.side == OrderSide.SELL:
                # Remove from inventory using FIFO and calculate realized PnL
                await self._process_sell_order(order, fees)

            # Update total fees paid
            self.metrics.total_fees_paid += fees

        except Exception as e:
            self.logger.error(f"Error updating inventory and PnL: {e}")

    async def _process_sell_order(self, sell_order: Order, sell_fees: Decimal) -> None:
        """Process a sell order against inventory using FIFO"""
        try:
            remaining_quantity = Decimal(str(sell_order.filled_quantity))
            sell_price = Decimal(str(sell_order.price))

            while remaining_quantity > 0 and self.inventory:
                # Get the oldest position (FIFO)
                oldest_position = self.inventory[0]

                if oldest_position.quantity <= remaining_quantity:
                    # Sell entire position
                    quantity_sold = oldest_position.quantity
                    remaining_quantity -= quantity_sold

                    # Calculate realized PnL for this portion
                    realized_pnl = await self._calculate_realized_pnl(
                        quantity_sold, oldest_position.cost_basis, sell_price,
                        oldest_position.fees_paid, sell_fees *
                        (quantity_sold / Decimal(str(sell_order.filled_quantity)))
                    )

                    # Create realized trade record
                    realized_trade = RealizedTrade(
                        buy_order_id=oldest_position.order_id,
                        sell_order_id=sell_order.id,
                        quantity=quantity_sold,
                        buy_price=oldest_position.cost_basis,
                        sell_price=sell_price,
                        buy_fees=oldest_position.fees_paid,
                        sell_fees=sell_fees *
                        (quantity_sold / Decimal(str(sell_order.filled_quantity))),
                        realized_pnl=realized_pnl,
                        timestamp=datetime.now()
                    )
                    self.realized_trades.append(realized_trade)

                    # Update metrics
                    self.metrics.realized_pnl += realized_pnl
                    if realized_pnl > 0:
                        self.metrics.profitable_trades += 1

                    # Remove the position from inventory
                    self.inventory.popleft()

                else:
                    # Partial sell - reduce position quantity
                    quantity_sold = remaining_quantity
                    oldest_position.quantity -= remaining_quantity

                    # Calculate realized PnL for this portion
                    realized_pnl = await self._calculate_realized_pnl(
                        quantity_sold, oldest_position.cost_basis, sell_price,
                        oldest_position.fees_paid *
                        (quantity_sold / (oldest_position.quantity + quantity_sold)),
                        sell_fees * (quantity_sold /
                                     Decimal(str(sell_order.filled_quantity)))
                    )

                    # Create realized trade record
                    realized_trade = RealizedTrade(
                        buy_order_id=oldest_position.order_id,
                        sell_order_id=sell_order.id,
                        quantity=quantity_sold,
                        buy_price=oldest_position.cost_basis,
                        sell_price=sell_price,
                        buy_fees=oldest_position.fees_paid *
                        (quantity_sold / (oldest_position.quantity + quantity_sold)),
                        sell_fees=sell_fees *
                        (quantity_sold / Decimal(str(sell_order.filled_quantity))),
                        realized_pnl=realized_pnl,
                        timestamp=datetime.now()
                    )
                    self.realized_trades.append(realized_trade)

                    # Update metrics
                    self.metrics.realized_pnl += realized_pnl
                    if realized_pnl > 0:
                        self.metrics.profitable_trades += 1

                    # Reduce fees proportionally
                    oldest_position.fees_paid -= oldest_position.fees_paid * \
                        (quantity_sold / (oldest_position.quantity + quantity_sold))
                    remaining_quantity = Decimal('0')

            if remaining_quantity > 0:
                self.logger.warning(
                    f"Sold {remaining_quantity} more than inventory - possible tracking error")

        except Exception as e:
            self.logger.error(f"Error processing sell order: {e}")

    async def _calculate_realized_pnl(self, quantity: Decimal, buy_price: Decimal, sell_price: Decimal,
                                      buy_fees: Decimal, sell_fees: Decimal) -> Decimal:
        """Calculate realized PnL for a buy-sell pair"""
        try:
            # Revenue from sale (after fees)
            gross_revenue = quantity * sell_price
            net_revenue = gross_revenue - sell_fees

            # Cost of purchase (including fees)
            gross_cost = quantity * buy_price
            total_cost = gross_cost + buy_fees

            # Realized PnL
            realized_pnl = net_revenue - total_cost

            return realized_pnl

        except Exception as e:
            self.logger.error(f"Error calculating realized PnL: {e}")
            return Decimal('0')

    # ============================================================================
    # CAPITAL TRACKING AND RISK MANAGEMENT METHODS
    # ============================================================================

    async def _initialize_capital_tracking(self) -> None:
        """Initialize capital tracking for risk management"""
        try:
            # Calculate initial capital value
            current_equity = await self._calculate_current_equity()
            self.initial_capital = current_equity
            self.high_water_mark = current_equity

            # Create initial equity snapshot
            snapshot = await self._create_equity_snapshot()
            self.equity_snapshots.append(snapshot)

            self.logger.info(
                f"Initial capital tracking initialized: {self.initial_capital}")

        except Exception as e:
            self.logger.error(f"Error initializing capital tracking: {e}")

    async def _calculate_current_equity(self) -> Decimal:
        """Calculate current total equity value"""
        try:
            # Get current balances
            balances = await self.brokerage.get_account_balance()
            base_balance = Decimal('0')
            quote_balance = Decimal('0')

            for balance in balances:
                if balance.asset == self.base_asset:
                    base_balance = Decimal(str(balance.total))
                elif balance.asset == self.quote_asset:
                    quote_balance = Decimal(str(balance.total))

            # Get current market price
            current_price = self.price_history[-1][1] if self.price_history else Decimal(
                '0')

            # Calculate total equity
            base_value = base_balance * current_price
            total_equity = base_value + quote_balance

            return total_equity

        except Exception as e:
            self.logger.error(f"Error calculating current equity: {e}")
            return Decimal('0')

    async def _create_equity_snapshot(self) -> EquitySnapshot:
        """Create an equity snapshot at current time"""
        try:
            # Get current balances
            balances = await self.brokerage.get_account_balance()
            base_balance = Decimal('0')
            quote_balance = Decimal('0')

            for balance in balances:
                if balance.asset == self.base_asset:
                    base_balance = Decimal(str(balance.total))
                elif balance.asset == self.quote_asset:
                    quote_balance = Decimal(str(balance.total))

            # Get current market price
            current_price = self.price_history[-1][1] if self.price_history else Decimal(
                '0')
            base_value = base_balance * current_price
            total_equity = base_value + quote_balance

            # Calculate unrealized PnL from current inventory
            unrealized_pnl = await self._calculate_unrealized_pnl(current_price)

            snapshot = EquitySnapshot(
                timestamp=datetime.now(),
                base_asset_quantity=base_balance,
                base_asset_value=base_value,
                quote_asset_balance=quote_balance,
                total_equity=total_equity,
                unrealized_pnl=unrealized_pnl,
                realized_pnl=self.metrics.realized_pnl
            )

            return snapshot

        except Exception as e:
            self.logger.error(f"Error creating equity snapshot: {e}")
            return EquitySnapshot(
                timestamp=datetime.now(),
                base_asset_quantity=Decimal('0'),
                base_asset_value=Decimal('0'),
                quote_asset_balance=Decimal('0'),
                total_equity=Decimal('0'),
                unrealized_pnl=Decimal('0'),
                realized_pnl=Decimal('0')
            )

    async def _calculate_unrealized_pnl(self, current_price: Decimal) -> Decimal:
        """Calculate unrealized PnL from current inventory"""
        try:
            unrealized_pnl = Decimal('0')

            for position in self.inventory:
                # Current value of position
                current_value = position.quantity * current_price
                # Original cost (including fees)
                original_cost = position.quantity * position.cost_basis + position.fees_paid
                # Unrealized PnL for this position
                position_pnl = current_value - original_cost
                unrealized_pnl += position_pnl

            return unrealized_pnl

        except Exception as e:
            self.logger.error(f"Error calculating unrealized PnL: {e}")
            return Decimal('0')

    async def _monitor_risk_levels(self, market_data: MarketData) -> None:
        """Monitor risk levels and trigger protective actions if needed"""
        try:
            # Check if we should perform equity check
            now = datetime.now()
            if (self.last_equity_check is None or
                    (now - self.last_equity_check).total_seconds() >= self.equity_check_interval):

                await self._perform_equity_check(market_data)
                self.last_equity_check = now

        except Exception as e:
            self.logger.error(f"Error monitoring risk levels: {e}")

    async def _perform_equity_check(self, market_data: MarketData) -> None:
        """Perform comprehensive equity check and risk assessment"""
        try:
            # Calculate current equity
            current_equity = await self._calculate_current_equity()

            # Update high water mark
            if current_equity > self.high_water_mark:
                self.high_water_mark = current_equity

            # Calculate current drawdown
            if self.high_water_mark > 0:
                current_drawdown_amount = self.high_water_mark - current_equity
                current_drawdown_percent = (
                    current_drawdown_amount / self.high_water_mark) * Decimal('100')
                self.metrics.current_drawdown = current_drawdown_percent

                # Update max drawdown if current is worse
                if current_drawdown_percent > self.metrics.max_drawdown:
                    self.metrics.max_drawdown = current_drawdown_percent

            # Calculate stop loss from initial capital
            if self.initial_capital and self.initial_capital > 0:
                loss_from_initial = self.initial_capital - current_equity
                loss_percent = (loss_from_initial /
                                self.initial_capital) * Decimal('100')

                # Check stop loss trigger
                if loss_percent >= self.stop_loss_percent and not self.stop_loss_triggered:
                    await self._trigger_stop_loss(current_equity, loss_percent)

            # Check max drawdown trigger
            if (self.metrics.current_drawdown >= self.max_drawdown_percent and
                    not self.max_drawdown_triggered):
                await self._trigger_max_drawdown(current_equity, self.metrics.current_drawdown)

            # Create equity snapshot
            snapshot = await self._create_equity_snapshot()
            self.equity_snapshots.append(snapshot)

            # Keep only recent snapshots (last 1000)
            if len(self.equity_snapshots) > 1000:
                self.equity_snapshots = self.equity_snapshots[-1000:]

            self.logger.debug(f"Equity check: Current={current_equity}, "
                              f"Drawdown={self.metrics.current_drawdown:.2f}%, "
                              f"Max Drawdown={self.metrics.max_drawdown:.2f}%")

        except Exception as e:
            self.logger.error(f"Error performing equity check: {e}")

    async def _trigger_stop_loss(self, current_equity: Decimal, loss_percent: Decimal) -> None:
        """Trigger stop loss protective actions"""
        try:
            self.stop_loss_triggered = True
            self.logger.critical(f"STOP LOSS TRIGGERED! Current equity: {current_equity}, "
                                 f"Loss: {loss_percent:.2f}%")

            # Cancel all active orders
            await self._cancel_all_orders()

            # Liquidate all base asset holdings
            await self._liquidate_inventory()

            # Halt trading
            self.is_running = False

            self.logger.critical(
                "Stop loss actions completed - strategy halted")

        except Exception as e:
            self.logger.error(f"Error triggering stop loss: {e}")

    async def _trigger_max_drawdown(self, current_equity: Decimal, drawdown_percent: Decimal) -> None:
        """Trigger max drawdown protective actions"""
        try:
            self.max_drawdown_triggered = True
            self.logger.critical(f"MAX DRAWDOWN TRIGGERED! Current equity: {current_equity}, "
                                 f"Drawdown: {drawdown_percent:.2f}%")

            # Cancel all active orders
            await self._cancel_all_orders()

            # Liquidate all base asset holdings
            await self._liquidate_inventory()

            # Halt trading
            self.is_running = False

            self.logger.critical(
                "Max drawdown actions completed - strategy halted")

        except Exception as e:
            self.logger.error(f"Error triggering max drawdown: {e}")

    async def _liquidate_inventory(self) -> None:
        """Liquidate all base asset holdings"""
        try:
            if not self.inventory:
                return

            # Calculate total inventory quantity
            total_quantity = sum(
                position.quantity for position in self.inventory)

            if total_quantity <= 0:
                return

            # Get current market price
            current_price = self.price_history[-1][1] if self.price_history else Decimal(
                '0')

            # Place market sell order to liquidate inventory
            order_request = OrderRequest(
                symbol=self.symbol,
                side=OrderSide.SELL,
                order_type=OrderType.MARKET,
                quantity=float(total_quantity),
                time_in_force='IOC'  # Immediate or Cancel
            )

            try:
                order = await self.brokerage.place_order(order_request)
                self.logger.info(
                    f"Liquidation order placed: {order.id} for {total_quantity} @ market")
            except Exception as e:
                self.logger.error(f"Failed to place liquidation order: {e}")

        except Exception as e:
            self.logger.error(f"Error liquidating inventory: {e}")

    async def _risk_monitor_loop(self) -> None:
        """Background task for continuous risk monitoring"""
        while not self._shutdown_requested:
            try:
                if self.is_running and self.price_history:
                    # Get current market data for risk assessment
                    market_data = await self.brokerage.get_market_data(self.symbol)
                    await self._monitor_risk_levels(market_data)

                await asyncio.sleep(self.equity_check_interval)

            except Exception as e:
                self.logger.error(f"Risk monitor loop error: {e}")
                await asyncio.sleep(30)

    # ============================================================================
    # UPDATED METHODS WITH PROPER DECIMAL USAGE AND FEE INTEGRATION
    # ============================================================================

    async def _update_trade_metrics_with_fees(self, order: Order, fees: Decimal) -> None:
        """Update trading metrics with proper fee accounting"""
        try:
            self.metrics.total_trades += 1
            self.metrics.total_volume += Decimal(str(order.filled_quantity))
            self.metrics.total_fees_paid += fees

            # Update unrealized PnL
            if self.price_history:
                current_price = self.price_history[-1][1]
                self.metrics.unrealized_pnl = await self._calculate_unrealized_pnl(current_price)

            # Update average profit per trade
            if self.metrics.total_trades > 0:
                total_pnl = self.metrics.realized_pnl + self.metrics.unrealized_pnl
                self.metrics.avg_profit_per_trade = total_pnl / \
                    Decimal(str(self.metrics.total_trades))

            # Update total profit (realized + unrealized - fees)
            self.metrics.total_profit = self.metrics.realized_pnl + \
                self.metrics.unrealized_pnl - self.metrics.total_fees_paid

        except Exception as e:
            self.logger.error(f"Error updating trade metrics with fees: {e}")

    async def _get_available_balances(self) -> Tuple[Decimal, Decimal]:
        """Get available balances for base and quote assets (updated to use Decimal)"""
        balances = await self.brokerage.get_account_balance()

        base_balance = Decimal('0')
        quote_balance = Decimal('0')

        for balance in balances:
            if balance.asset == self.base_asset:
                base_balance = Decimal(str(balance.free))
            elif balance.asset == self.quote_asset:
                quote_balance = Decimal(str(balance.free))

        current_price = self.price_history[-1][1] if self.price_history else Decimal(
            '0')

        # Handle total amount configuration
        if self.total_amount == -1:
            # Use all available funds, but balance them equally between buy and sell sides
            base_value = base_balance * current_price
            total_value = base_value + quote_balance

            # Split total value equally between buy and sell sides
            half_value = total_value / Decimal('2')

            # Convert back to asset amounts
            # For buy orders: use quote asset (half_value in quote)
            # For sell orders: use base asset (half_value converted to base)
            balanced_quote_balance = half_value
            balanced_base_balance = half_value / \
                current_price if current_price > 0 else Decimal('0')

            # Use the minimum of available balance and calculated balanced amount
            base_balance = min(base_balance, balanced_base_balance)
            quote_balance = min(quote_balance, balanced_quote_balance)

        elif self.total_amount > 0:
            # Use specified total amount, split equally between buy and sell
            half_amount = self.total_amount / Decimal('2')

            # For buy orders: use quote asset (half_amount in quote)
            # For sell orders: use base asset (half_amount converted to base)
            target_quote_balance = half_amount
            target_base_balance = half_amount / \
                current_price if current_price > 0 else Decimal('0')

            # Use the minimum of available balance and target amount
            base_balance = min(base_balance, target_base_balance)
            quote_balance = min(quote_balance, target_quote_balance)

        # Apply position size percentage
        base_balance *= (self.position_size_percent / Decimal('100'))
        quote_balance *= (self.position_size_percent / Decimal('100'))

        return base_balance, quote_balance

    async def _calculate_level_quantities(self, base_balance: Decimal, quote_balance: Decimal) -> Tuple[Decimal, Decimal]:
        """Calculate order quantities for each grid level (updated to use Decimal)"""
        current_price = self.price_history[-1][1] if self.price_history else Decimal(
            '0')

        # Calculate how many levels will be buy vs sell orders
        current_level_index = await self._find_nearest_grid_level(current_price)
        buy_levels = current_level_index + 1  # Levels below current price
        sell_levels = self.grid_levels - current_level_index  # Levels above current price

        # Calculate quantities
        buy_quantity_per_level = Decimal('0')
        sell_quantity_per_level = Decimal('0')

        if buy_levels > 0 and quote_balance > 0:
            # For buy orders, we need to calculate the quantity based on available quote balance
            # and the number of buy levels

            # Simple approach: divide quote balance equally among buy levels
            # Each buy order will use: quote_balance / buy_levels
            quote_per_level = quote_balance / Decimal(str(buy_levels))

            # Calculate average price for buy levels to estimate quantity
            if self.grid_levels_data and len(self.grid_levels_data) > buy_levels:
                # Use actual grid level prices
                avg_buy_price = sum(
                    self.grid_levels_data[i].price for i in range(buy_levels)
                ) / Decimal(str(buy_levels))
            else:
                # Fallback to current price
                avg_buy_price = current_price

            # Calculate quantity per level: quote_amount / price
            buy_quantity_per_level = quote_per_level / \
                avg_buy_price if avg_buy_price > 0 else Decimal('0')

        if sell_levels > 0 and base_balance > 0:
            # For sell orders, simply divide base balance among sell levels
            sell_quantity_per_level = base_balance / Decimal(str(sell_levels))

        return buy_quantity_per_level, sell_quantity_per_level

    async def _place_opposite_order(self, filled_order: Order) -> None:
        """Place opposite order after a fill"""
        try:
            # Find the corresponding grid level
            for level in self.grid_levels_data:
                if abs(level.price - filled_order.price) < self.grid_spacing / 2:

                    if filled_order.side == OrderSide.BUY:
                        # Place sell order at higher level
                        higher_level = await self._find_higher_grid_level(level)
                        if higher_level and not higher_level.sell_order_id:
                            sell_order = await self._place_grid_order(
                                side=OrderSide.SELL,
                                price=higher_level.price,
                                quantity=Decimal(
                                    str(filled_order.filled_quantity))
                            )
                            if sell_order:
                                higher_level.sell_order_id = sell_order.id
                                self.active_orders[sell_order.id] = sell_order

                    else:  # filled_order.side == OrderSide.SELL
                        # Place buy order at lower level
                        lower_level = await self._find_lower_grid_level(level)
                        if lower_level and not lower_level.buy_order_id:
                            buy_order = await self._place_grid_order(
                                side=OrderSide.BUY,
                                price=lower_level.price,
                                quantity=Decimal(
                                    str(filled_order.filled_quantity))
                            )
                            if buy_order:
                                lower_level.buy_order_id = buy_order.id
                                self.active_orders[buy_order.id] = buy_order

                    break  # Exit loop once we find and process the matching level

        except Exception as e:
            self.logger.error(
                f"Failed to place opposite order for {filled_order.id}: {e}")

    async def _cancel_all_orders(self) -> None:
        """Cancel all active orders"""
        try:
            for order_id, order in list(self.active_orders.items()):
                try:
                    success = await self.brokerage.cancel_order(order_id, self.symbol)
                    if success:
                        del self.active_orders[order_id]
                        await self._update_grid_level_after_cancel(order)
                        self.logger.info(f"Cancelled order: {order_id}")
                except Exception as e:
                    self.logger.error(
                        f"Failed to cancel order {order_id}: {e}")

            self.logger.info(
                f"Cancelled all orders - {len(self.active_orders)} orders remaining")

        except Exception as e:
            self.logger.error(f"Error cancelling all orders: {e}")
