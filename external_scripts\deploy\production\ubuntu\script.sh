#!/bin/bash

# --- Configuration ---
# Adjust these variables to match your setup

# The directory of your Git repository and Python project
PROJECT_DIR="/home/<USER>/apps/trading_bot_manager"

# The name of your virtual environment directory (relative to PROJECT_DIR)
VENV_DIR=".venv"

# The name of your requirements file (relative to PROJECT_DIR)
REQUIREMENTS_FILE="requirements.txt"

# The name of the systemd service to restart
SYSTEMD_SERVICE_NAME="trading_bot_manager.service"

# The user that owns the project files and should run pip install
# This script will likely be run as root (for systemctl),
# so we might need to switch user for pip install if necessary,
# or ensure the venv is writable by root (less ideal).
# For simplicity, this script assumes the user running it has permissions,
# or that commands like pip install will work correctly.
# If you run this script as root, `sudo -u your_project_user` might be needed for pip.
PROJECT_USER="linuxuser"

# --- Script Logic ---

# Function to print messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Function to handle errors
handle_error() {
    log_message "ERROR: $1"
    exit 1
}

# Check for the correct number of arguments
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <command>"
    echo "Available commands: update"
    exit 1
fi

COMMAND=$1

# --- Update Command Logic ---
if [ "$COMMAND" == "update" ]; then
    log_message "Starting update process..."

    # 1. Navigate to the project directory
    log_message "Changing directory to ${PROJECT_DIR}..."
    cd "${PROJECT_DIR}" || handle_error "Could not change to project directory: ${PROJECT_DIR}"

    # 2. Pull latest changes from Git
    log_message "Pulling latest changes from Git..."
    git pull || handle_error "Git pull failed."
    log_message "Git pull successful."

    # 3. Activate virtual environment and update dependencies
    VENV_PATH="${PROJECT_DIR}/${VENV_DIR}"
    REQUIREMENTS_PATH="${PROJECT_DIR}/${REQUIREMENTS_FILE}"

    if [ -d "${VENV_PATH}" ]; then
        log_message "Activating virtual environment: ${VENV_PATH}"
        # Source the activate script. Note: this only affects the current subshell of the script.
        # The actual pip command needs to use the python/pip from the venv.
        # Using the direct path to pip in the venv is more robust.
        PIP_EXEC="${VENV_PATH}/bin/pip"

        if [ ! -f "${PIP_EXEC}" ]; then
            handle_error "Pip executable not found in virtual environment: ${PIP_EXEC}"
        fi

        if [ -f "${REQUIREMENTS_PATH}" ]; then
            log_message "Updating dependencies from ${REQUIREMENTS_FILE}..."
            # Run pip install as the project user if the script is run as root
            # and the venv is owned by the project user.
            # This is a common pattern but might need adjustment based on your permissions.
            # If the script is already run by PROJECT_USER, sudo -u is not needed.
            # If the venv is writable by root (e.g., venv created by root), then direct call is fine.
            # For now, assuming direct call works or script is run by appropriate user.
            "${PIP_EXEC}" install -r "${REQUIREMENTS_PATH}" || handle_error "Failed to update dependencies."
            log_message "Dependencies updated successfully."
        else
            log_message "WARNING: Requirements file not found at ${REQUIREMENTS_PATH}. Skipping dependency update."
        fi
    else
        log_message "WARNING: Virtual environment directory not found at ${VENV_PATH}. Skipping dependency update."
    fi

    # 4. Restart the systemd service
    log_message "Restarting systemd service: ${SYSTEMD_SERVICE_NAME}..."
    # This command usually requires sudo privileges.
    # If the script is not run as root, this will fail.
    # Ensure the user running this script has sudo rights for systemctl or run the script with sudo.
    systemctl restart "${SYSTEMD_SERVICE_NAME}" || handle_error "Failed to restart systemd service: ${SYSTEMD_SERVICE_NAME}."
    log_message "Service ${SYSTEMD_SERVICE_NAME} restarted successfully."

    # Optional: Check service status after restart
    sleep 2 # Give the service a moment to restart
    log_message "Checking status of service ${SYSTEMD_SERVICE_NAME}..."
    systemctl status "${SYSTEMD_SERVICE_NAME}" --no-pager || log_message "Could not get status for ${SYSTEMD_SERVICE_NAME} (it might still be starting/failed)."

    log_message "Update process completed."

else
    echo "Invalid command: $COMMAND"
    echo "Available commands: update"
    exit 1
fi

exit 0