from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import asyncio
from .common import BotStatus
from .i_strategy import IStrategy
from .i_brokerage import IBrokerage

class IBot(ABC):
    """
    Abstract base class for trading bots.

    This interface defines the contract that all trading bots must implement.
    It provides no concrete implementation to maintain clean separation of concerns.
    """

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the bot and its components"""
        pass

    @abstractmethod
    async def start(self) -> None:
        """Start the bot operation"""
        pass

    @abstractmethod
    async def stop(self) -> None:
        """Stop the bot operation gracefully"""
        pass

    @abstractmethod
    async def pause(self) -> None:
        """Pause the bot operation"""
        pass

    @abstractmethod
    async def resume(self) -> None:
        """Resume the bot operation from paused state"""
        pass

    @abstractmethod
    async def run(self) -> None:
        """Main bot execution loop"""
        pass

    @abstractmethod
    async def handle_error(self, error: Exception) -> None:
        """Handle errors during bot execution"""
        pass

    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """Get basic bot status information (synchronous)"""
        pass

    @abstractmethod
    async def get_bot_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status (asynchronous)"""
        pass

    @abstractmethod
    async def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update bot configuration"""
        pass

    @abstractmethod
    async def get_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get bot logs"""
        pass

    @abstractmethod
    def set_strategy(self, strategy: IStrategy) -> None:
        """Set the trading strategy"""
        pass

    @abstractmethod
    def set_brokerage(self, brokerage: IBrokerage) -> None:
        """Set the brokerage"""
        pass

    # Abstract properties that must be implemented
    @property
    @abstractmethod
    def name(self) -> str:
        """Bot name"""
        pass

    @property
    @abstractmethod
    def config(self) -> Dict[str, Any]:
        """Bot configuration"""
        pass

    @property
    @abstractmethod
    def status(self) -> BotStatus:
        """Current bot status"""
        pass

    @property
    @abstractmethod
    def is_running(self) -> bool:
        """Check if bot is currently running"""
        pass

    @property
    @abstractmethod
    def is_paused(self) -> bool:
        """Check if bot is currently paused"""
        pass

    @property
    @abstractmethod
    def task(self) -> Optional[asyncio.Task]:
        """Get the asyncio task associated with the bot"""
        pass

    @property
    @abstractmethod
    def strategy(self) -> Optional[IStrategy]:
        """Get the bot's strategy"""
        pass

    @property
    @abstractmethod
    def brokerage(self) -> Optional[IBrokerage]:
        """Get the bot's brokerage"""
        pass