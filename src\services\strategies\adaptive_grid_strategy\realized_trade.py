from decimal import Decimal
from datetime import datetime
from dataclasses import dataclass


@dataclass
class RealizedTrade:
    """Represents a completed buy-sell pair with realized profit/loss"""
    buy_order_id: str
    sell_order_id: str
    quantity: Decimal
    buy_price: Decimal
    sell_price: Decimal
    buy_fees: Decimal
    sell_fees: Decimal
    realized_pnl: Decimal
    timestamp: datetime
