from decimal import Decimal
from datetime import datetime, timedelta
from typing import Optional
from dataclasses import dataclass, field


@dataclass
class StrategyMetrics:
    """Strategy performance metrics"""
    total_trades: int = 0
    profitable_trades: int = 0
    total_profit: Decimal = Decimal('0')
    total_volume: Decimal = Decimal('0')
    avg_profit_per_trade: Decimal = Decimal('0')
    grid_resets: int = 0
    last_reset: Optional[datetime] = None
    uptime: timedelta = field(default_factory=lambda: timedelta())
    total_fees_paid: Decimal = Decimal('0')
    realized_pnl: Decimal = Decimal('0')
    unrealized_pnl: Decimal = Decimal('0')
    max_drawdown: Decimal = Decimal('0')
    current_drawdown: Decimal = Decimal('0')
