"""
Core bot module providing base bot functionality.
This module provides a solid foundation for all bot implementations
with common lifecycle management, error handling, and status tracking.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import asyncio
import logging
from datetime import datetime
from collections import deque

from .interfaces import IBot, BotStatus, IStrategy, IBrokerage


class BaseBot(IBot, ABC):
    """
    Base implementation of IBot providing common functionality
    for all trading bots.

    This class handles:
    - Lifecycle management (start/stop/pause/resume)
    - Status tracking and reporting
    - Error handling and recovery
    - Basic logging functionality
    - Configuration management
    """

    def __init__(self, name: str, config: Dict[str, Any],
                 strategy: Optional[IStrategy] = None,
                 brokerage: Optional[IBrokerage] = None,
                 logger: Optional[logging.Logger] = None):
        self._name = name
        self._config = config.copy()
        self._strategy = strategy
        self._brokerage = brokerage
        self._logger = logger or logging.getLogger(f"Bot.{name}")

        # Status and lifecycle management
        self._status = BotStatus.STOPPED
        self._task: Optional[asyncio.Task] = None
        self._stop_event = asyncio.Event()
        self._pause_event = asyncio.Event()
        self._start_time: Optional[datetime] = None
        self._last_activity: Optional[datetime] = None

        # Error tracking
        self._error_count = 0
        self._last_error: Optional[Exception] = None
        self._max_errors = config.get('max_errors', 10)

        # Logging
        self._logs = deque(maxlen=config.get('max_logs', 1000))

        # Configuration
        self._update_interval = config.get('update_interval', 5)

        # Set initial pause state (not paused)
        self._pause_event.set()

    # Properties implementation
    @property
    def name(self) -> str:
        """Bot name"""
        return self._name

    @property
    def config(self) -> Dict[str, Any]:
        """Bot configuration"""
        return self._config.copy()

    @property
    def status(self) -> BotStatus:
        """Current bot status"""
        return self._status

    @property
    def is_running(self) -> bool:
        """Check if the bot is currently running"""
        return self._status == BotStatus.RUNNING and self._task is not None and not self._task.done()

    @property
    def is_paused(self) -> bool:
        """Check if the bot is currently paused"""
        return self._status == BotStatus.PAUSED

    @property
    def task(self) -> Optional[asyncio.Task]:
        """Get the asyncio task associated with the bot"""
        return self._task

    @property
    def strategy(self) -> Optional[IStrategy]:
        """Get the bot's strategy"""
        return self._strategy

    @property
    def brokerage(self) -> Optional[IBrokerage]:
        """Get the bot's brokerage"""
        return self._brokerage

    @property
    def uptime(self) -> Optional[float]:
        """Get bot uptime in seconds"""
        if self._start_time and self._status in [BotStatus.RUNNING, BotStatus.PAUSED]:
            return (datetime.now() - self._start_time).total_seconds()
        return None

    @property
    def error_count(self) -> int:
        """Get the number of errors encountered"""
        return self._error_count

    @property
    def last_activity(self) -> Optional[datetime]:
        """Get timestamp of last activity"""
        return self._last_activity

    # Lifecycle management
    async def start(self) -> None:
        """Start the bot operation"""
        if self.is_running:
            raise RuntimeError(f"Bot {self.name} is already running")

        self._log('info', f"Starting bot {self.name}")
        self._status = BotStatus.STARTING
        self._stop_event.clear()
        self._pause_event.set()

        try:
            await self.initialize()
            self._task = asyncio.create_task(self._run_with_error_handling())
            self._start_time = datetime.now()
            self._status = BotStatus.RUNNING
            self._error_count = 0
            self._update_activity()
            self._log('info', f"Bot {self.name} started successfully")
        except Exception as e:
            self._status = BotStatus.ERROR
            self._increment_error_count()
            self._log('error', f"Failed to start bot {self.name}: {e}")
            raise

    async def stop(self) -> None:
        """Stop the bot operation gracefully"""
        if self._status not in [BotStatus.RUNNING, BotStatus.PAUSED]:
            return

        self._log('info', f"Stopping bot {self.name}")
        self._status = BotStatus.STOPPING
        self._stop_event.set()
        self._pause_event.set()  # Ensure bot can exit pause state

        if self._task:
            try:
                await asyncio.wait_for(self._task, timeout=30.0)
            except asyncio.TimeoutError:
                self._log('warning', f"Bot {self.name} did not stop gracefully, cancelling task")
                self._task.cancel()
                try:
                    await self._task
                except asyncio.CancelledError:
                    pass

        await self._cleanup()
        self._status = BotStatus.STOPPED
        self._task = None
        self._start_time = None
        self._log('info', f"Bot {self.name} stopped")

    async def pause(self) -> None:
        """Pause the bot operation"""
        if self._status != BotStatus.RUNNING:
            raise RuntimeError(f"Bot {self.name} is not running")

        self._log('info', f"Pausing bot {self.name}")
        self._status = BotStatus.PAUSED
        self._pause_event.clear()

    async def resume(self) -> None:
        """Resume the bot operation from paused state"""
        if self._status != BotStatus.PAUSED:
            raise RuntimeError(f"Bot {self.name} is not paused")

        self._log('info', f"Resuming bot {self.name}")
        self._status = BotStatus.RUNNING
        self._pause_event.set()

    async def _run_with_error_handling(self) -> None:
        """Run the bot with error handling and recovery"""
        try:
            await self.run()
        except asyncio.CancelledError:
            self._log('info', f"Bot {self.name} main loop cancelled")
        except Exception as e:
            self._status = BotStatus.ERROR
            self._increment_error_count()
            self._log('error', f"Fatal error in bot {self.name}: {e}")
            await self.handle_error(e)
        finally:
            await self._cleanup()
            if self._status == BotStatus.RUNNING:
                self._status = BotStatus.STOPPED

    # Error handling and recovery
    async def handle_error(self, error: Exception) -> None:
        """Handle errors during bot execution"""
        self._last_error = error
        self._increment_error_count()

        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'error_count': self._error_count
        }

        self._log('error', f"Error #{self._error_count}: {error}", error_data)

        # If too many errors, stop the bot
        if self._error_count >= self._max_errors:
            self._log('critical', f"Max errors ({self._max_errors}) reached, stopping bot")
            self._status = BotStatus.ERROR
            await self.stop()
        else:
            # Brief pause before retrying (exponential backoff capped at 60s)
            await asyncio.sleep(min(5 * self._error_count, 60))

    # Status and information methods
    def get_status(self) -> Dict[str, Any]:
        """Get basic bot status information (synchronous)"""
        return {
            "name": self.name,
            "status": self._status.value,
            "is_running": self.is_running,
            "is_paused": self.is_paused,
            "uptime": self.uptime,
            "error_count": self._error_count,
            "last_activity": self._last_activity.isoformat() if self._last_activity else None
        }

    async def get_bot_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status (asynchronous)"""
        task_info = None
        if self._task:
            task_info = {
                'done': self._task.done(),
                'cancelled': self._task.cancelled(),
                'exception': str(self._task.exception()) if self._task.done() and self._task.exception() else None
            }

        return {
            'name': self.name,
            'status': self._status.value,
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'uptime': self.uptime,
            'start_time': self._start_time.isoformat() if self._start_time else None,
            'last_activity': self._last_activity.isoformat() if self._last_activity else None,
            'strategy': {
                'name': self._strategy.__class__.__name__ if self._strategy else None,
                'config': self._strategy.config if self._strategy else None
            },
            'brokerage': {
                'name': self._brokerage.__class__.__name__ if self._brokerage else None,
                'connected': self._brokerage.is_connected if self._brokerage else False
            },
            'error_info': {
                'last_error': str(self._last_error) if self._last_error else None,
                'error_count': self._error_count,
                'max_errors': self._max_errors
            },
            'task_info': task_info,
            'log_count': len(self._logs),
            'config': self._config
        }

    async def get_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get bot logs"""
        logs = list(self._logs)

        # Convert datetime objects to strings for JSON serialization
        serialized_logs = []
        for log in logs[-limit:]:
            serialized_log = log.copy()
            if isinstance(serialized_log.get('timestamp'), datetime):
                serialized_log['timestamp'] = serialized_log['timestamp'].isoformat()
            serialized_logs.append(serialized_log)

        return serialized_logs

    async def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update bot configuration"""
        old_config = self._config.copy()

        try:
            # Update configuration
            self._config.update(new_config)

            # Update derived properties
            self._update_interval = self._config.get('update_interval', 5)
            self._max_errors = self._config.get('max_errors', 10)

            # Resize log deque if max_logs changed
            max_logs = self._config.get('max_logs', 1000)
            if max_logs != old_config.get('max_logs', 1000):
                new_logs = deque(self._logs, maxlen=max_logs)
                self._logs = new_logs

            self._log('info', f"Configuration updated", {'config_changes': new_config})

        except Exception as e:
            # Rollback configuration on error
            self._config = old_config
            self._log('error', f"Failed to update configuration: {e}")
            raise

    def set_strategy(self, strategy: IStrategy) -> None:
        """Set the trading strategy"""
        if self.is_running:
            raise RuntimeError("Cannot change strategy while bot is running")

        self._strategy = strategy
        self._log('info', f"Strategy set to {strategy.__class__.__name__}")

    def set_brokerage(self, brokerage: IBrokerage) -> None:
        """Set the brokerage"""
        if self.is_running:
            raise RuntimeError("Cannot change brokerage while bot is running")

        self._brokerage = brokerage
        self._log('info', f"Brokerage set to {brokerage.__class__.__name__}")

    # Utility methods
    def _update_activity(self):
        """Update the last activity timestamp"""
        self._last_activity = datetime.now()

    def _increment_error_count(self):
        """Increment the error counter"""
        self._error_count += 1

    def _log(self, level: str, message: str, extra_data: Dict[str, Any] = None) -> None:
        """Internal logging method that stores logs and forwards to logger"""
        log_entry = {
            'timestamp': datetime.now(),
            'level': level,
            'message': message,
            'bot_name': self.name,
            'status': self._status.value,
            **(extra_data or {})
        }

        self._logs.append(log_entry)

        # Forward to logger
        log_method = getattr(self._logger, level.lower(), self._logger.info)
        log_method(f"[{self.name}] {message}")

    async def _cleanup(self) -> None:
        """Cleanup resources - can be overridden by subclasses"""
        self._log('info', f"Starting cleanup for bot {self.name}")

        cleanup_errors = []

        try:
            if self._strategy:
                await self._strategy.shutdown()
        except Exception as e:
            cleanup_errors.append(f"Strategy cleanup failed: {e}")

        try:
            if self._brokerage:
                await self._brokerage.disconnect()
        except Exception as e:
            cleanup_errors.append(f"Brokerage cleanup failed: {e}")

        if cleanup_errors:
            error_msg = "; ".join(cleanup_errors)
            self._log('error', f"Cleanup errors: {error_msg}")
        else:
            self._log('info', f"Cleanup completed successfully for bot {self.name}")

    # Abstract methods that must be implemented by subclasses
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the bot and its components - must be implemented by subclasses"""
        pass

    @abstractmethod
    async def run(self) -> None:
        """Main bot execution loop - must be implemented by subclasses"""
        pass
