"""
Additional API routes for the trading bot management system.
"""
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends

from manager.bot_manager import BotManager
from core.exceptions import TradingBotNotFoundError
from api.schemas.bots_schemas import BotConfigUpdate
from api.dependencies import get_bot_manager

# Router instance
router = APIRouter()


@router.get("/", response_model=List[str])
async def list_bots(
    bot_manager: BotManager = Depends(get_bot_manager)
):
    """List all bot names"""
    return bot_manager.list_bots()


@router.get("/status", response_model=List[Dict[str, Any]])
async def get_all_bots_status(
    bot_manager: BotManager = Depends(get_bot_manager)
):
    """Get status of all bots"""
    return bot_manager.get_all_bots_status()


@router.get("/{bot_name}/status", response_model=Dict[str, Any])
async def get_bot_status(
    bot_name: str,
    bot_manager: BotManager = Depends(get_bot_manager)
):
    """Get status of a specific bot"""
    return bot_manager.get_bot_status(bot_name)


@router.post("/{bot_name}/start")
async def start_bot(bot_name: str, background_tasks: BackgroundTasks,
                    bot_manager: BotManager = Depends(get_bot_manager),
                    ):
    """Start a specific bot"""
    background_tasks.add_task(bot_manager.start_bot, bot_name)
    return {"message": f"Starting bot {bot_name}"}


@router.post("/{bot_name}/stop")
async def stop_bot(bot_name: str, background_tasks: BackgroundTasks,
                   bot_manager: BotManager = Depends(get_bot_manager)):
    """Stop a specific bot"""
    background_tasks.add_task(bot_manager.stop_bot, bot_name)
    return {"message": f"Stopping bot {bot_name}"}


@router.post("/{bot_name}/restart")
async def restart_bot(bot_name: str, background_tasks: BackgroundTasks,
                      bot_manager: BotManager = Depends(get_bot_manager)):
    """Restart a specific bot"""
    background_tasks.add_task(bot_manager.restart_bot, bot_name)
    return {"message": f"Restarting bot {bot_name}"}


@router.post("/start-all")
async def start_all_bots(background_tasks: BackgroundTasks,
                         bot_manager: BotManager = Depends(get_bot_manager)):
    """Start all bots"""
    background_tasks.add_task(bot_manager.start_all_bots)
    return {"message": "Starting all bots"}


@router.post("/stop-all")
async def stop_all_bots(background_tasks: BackgroundTasks,
                        bot_manager: BotManager = Depends(get_bot_manager)):
    """Stop all bots"""
    background_tasks.add_task(bot_manager.stop_all_bots)
    return {"message": "Stopping all bots"}


@router.get("/{bot_name}/logs", response_model=List[str])
async def get_bot_logs(
    bot_name: str,
    lines: int = 100,
    bot_manager: BotManager = Depends(get_bot_manager)
) -> List[str]:
    """Get recent log lines for a specific bot."""
    try:
        # This would need to be implemented in BotManager
        logs = await bot_manager.get_bot_logs(bot_name, lines)
        return logs
    except TradingBotNotFoundError:
        raise HTTPException(
            status_code=404, detail=f"Bot '{bot_name}' not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{bot_name}/metrics", response_model=Dict[str, Any])
async def get_bot_metrics(
    bot_name: str,
    bot_manager: BotManager = Depends(get_bot_manager)
) -> Dict[str, Any]:
    """Get performance metrics for a specific bot."""
    try:
        metrics = await bot_manager.get_bot_metrics(bot_name)
        return metrics
    except TradingBotNotFoundError:
        raise HTTPException(
            status_code=404, detail=f"Bot '{bot_name}' not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{bot_name}/config")
async def update_bot_config(
    bot_name: str,
    config_update: BotConfigUpdate,
    background_tasks: BackgroundTasks,
    bot_manager: BotManager = Depends(get_bot_manager)
) -> Dict[str, str]:
    """Update configuration for a specific bot."""
    try:
        background_tasks.add_task(
            bot_manager.update_bot_config,
            bot_name,
            config_update.model_dump(exclude_unset=True)
        )
        return {"message": f"Configuration update for bot '{bot_name}' initiated"}
    except TradingBotNotFoundError:
        raise HTTPException(
            status_code=404, detail=f"Bot '{bot_name}' not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{bot_name}/orders", response_model=List[Dict[str, Any]])
async def get_bot_orders(
    bot_name: str,
    limit: int = 50,
    bot_manager: BotManager = Depends(get_bot_manager)
) -> List[Dict[str, Any]]:
    """Get recent orders for a specific bot."""
    try:
        orders = await bot_manager.get_bot_orders(bot_name, limit)
        return orders
    except TradingBotNotFoundError:
        raise HTTPException(
            status_code=404, detail=f"Bot '{bot_name}' not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{bot_name}/positions", response_model=List[Dict[str, Any]])
async def get_bot_positions(
    bot_name: str,
    bot_manager: BotManager = Depends(get_bot_manager)
) -> List[Dict[str, Any]]:
    """Get current positions for a specific bot."""
    try:
        positions = await bot_manager.get_bot_positions(bot_name)
        return positions
    except TradingBotNotFoundError:
        raise HTTPException(
            status_code=404, detail=f"Bot '{bot_name}' not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{bot_name}/emergency-stop")
async def emergency_stop_bot(
    bot_name: str,
    bot_manager: BotManager = Depends(get_bot_manager)
) -> Dict[str, str]:
    """Emergency stop for a specific bot (immediate shutdown)."""
    try:
        await bot_manager.emergency_stop_bot(bot_name)
        return {"message": f"Emergency stop executed for bot '{bot_name}'"}
    except TradingBotNotFoundError:
        raise HTTPException(
            status_code=404, detail=f"Bot '{bot_name}' not found")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
