from typing import Optional
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

class BotStatus(Enum):
    """Bot status enumeration"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"
    PAUSED = "paused"


class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "BUY"
    SELL = "SELL"


class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"
    STOP_LOSS_LIMIT = "STOP_LOSS_LIMIT"
    TAKE_PROFIT_LIMIT = "TAKE_PROFIT_LIMIT"


class OrderStatus(Enum):
    """Order status enumeration"""
    NEW = "NEW"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    FILLED = "FILLED"
    CANCELED = "CANCELED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"


@dataclass
class Balance:
    """Account balance representation"""
    asset: str
    free: float
    locked: float
    total: float


@dataclass
class Ticker:
    """Price ticker representation"""
    symbol: str
    price: float
    timestamp: datetime
    change_24h: Optional[float] = None
    change_percent_24h: Optional[float] = None
    volume_24h: Optional[float] = None


@dataclass
class MarketData:
    """Market data representation"""
    symbol: str
    bid: float
    ask: float
    last_price: float
    volume: float
    timestamp: datetime
    high_24h: Optional[float] = None
    low_24h: Optional[float] = None
    open_24h: Optional[float] = None


@dataclass
class OrderRequest:
    """Order request representation"""
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float = None
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: Optional[str] = "GTC"
    client_order_id: Optional[str] = None


@dataclass
class Order:
    """Order representation"""
    id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float]
    status: str
    filled_quantity: float
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    stop_price: Optional[float] = None
    client_order_id: Optional[str] = None


@dataclass
class Trade:
    """Trade execution representation"""
    id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    fee: float
    fee_asset: str
    timestamp: datetime