from decimal import Decimal
from typing import Optional
from datetime import datetime
from dataclasses import dataclass


@dataclass
class GridLevel:
    """Represents a single level in the grid"""
    price: Decimal
    buy_order_id: Optional[str] = None
    sell_order_id: Optional[str] = None
    buy_quantity: Decimal = Decimal('0')
    sell_quantity: Decimal = Decimal('0')
    is_active: bool = True
    last_filled: Optional[datetime] = None
