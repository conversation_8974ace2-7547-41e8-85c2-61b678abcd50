from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request
from fastapi.responses import JSONResponse
from manager.config_loader import config


class AuthTokenMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Get the token from the request headers
        token = request.headers.get("Auth-Token", "")
        if token == config.manager.authentication.token or not config.manager.authentication.token:
            # Token is valid, continue to the next middleware
            return await call_next(request)
        else:
            # Token is invalid, return an error response
            return JSONResponse(status_code=401, content={"message": "Invalid token"})
