# src/main.py
from decimal import getcontext
from api.app import app
import asyncio
import uvicorn
import logging
import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

getcontext().prec = 28  # Set high precision for financial calculations


def setup_logging():
    """Setup basic logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/main.log')
        ]
    )


def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'config']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


async def main():
    """Main application entry point"""
    # Setup
    create_directories()
    setup_logging()

    # Start the application
    config = uvicorn.Config(
        app="api.app:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )

    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    asyncio.run(main())
