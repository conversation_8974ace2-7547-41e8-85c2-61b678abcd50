from decimal import Decimal
from datetime import datetime
from dataclasses import dataclass


@dataclass
class EquitySnapshot:
    """Snapshot of strategy equity at a point in time"""
    timestamp: datetime
    base_asset_quantity: Decimal
    base_asset_value: Decimal  # At current market price
    quote_asset_balance: Decimal
    total_equity: Decimal
    unrealized_pnl: Decimal
    realized_pnl: Decimal
