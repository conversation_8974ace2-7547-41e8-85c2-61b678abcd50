# src/services/bots/trading_bot.py
"""
Trading bot implementation focused on trading-specific functionality.
This class extends BaseBot with trading logic, database operations, and market analysis.
"""

import asyncio
import logging
from typing import Dict, Any
from datetime import datetime

from core.interfaces import IStrategy, IBrokerage, IDatabase
from core.bot import BaseBot
from core.interfaces.common import OrderRequest


class TradingBot(BaseBot):
    """
    Trading bot implementation with trading-specific functionality.

    This class extends BaseBot with:
    - Database integration for order/trade storage
    - Market data processing and analysis
    - Order execution and management
    - Trading mode support (test/live)
    """

    def __init__(
        self,
        name: str,
        config: Dict[str, Any],
        strategy: IStrategy = None,
        brokerage: IBrokerage = None,
        database: IDatabase = None,
        logger: logging.Logger = None
    ):
        super().__init__(name, config, strategy, brokerage, logger)
        self.database = database
        self.mode = config.get('mode', 'test')

        # Trading-specific configuration
        self._order_timeout = config.get('order_timeout', 30)
        self._max_position_size = config.get('max_position_size', 1000.0)

    async def initialize(self) -> None:
        """Initialize the trading bot and its components"""
        self._log('info', f"Initializing trading bot {self.name}")

        try:
            # Validate required components for trading
            if not self.strategy:
                raise ValueError("Strategy is required for trading bot")
            if not self.brokerage:
                raise ValueError("Brokerage is required for trading bot")
            if not self.database:
                raise ValueError("Database is required for trading bot")

            # Initialize components in order
            await self.database.connect()
            await self.brokerage.connect()
            # Set strategy and brokerage references
            await self.strategy.initialize(self.brokerage)

            self._log('info', f"Trading bot {self.name} initialized successfully")

        except Exception as e:
            self._log('error', f"Failed to initialize trading bot {self.name}: {e}")
            raise

    async def run(self) -> None:
        """Main trading bot execution loop"""
        self._log('info', f"Trading bot {self.name} entering main loop")

        try:
            while not self._stop_event.is_set():
                try:
                    # Wait if paused
                    await self._pause_event.wait()

                    # Check if we should shutdown after unpausing
                    if self._stop_event.is_set():
                        break

                    # Wait for next iteration (respect pause state)
                    for _ in range(self._update_interval):
                        if self._stop_event.is_set():
                            break
                        await asyncio.sleep(1)
                        await self._pause_event.wait()

                except Exception as e:
                    await self.handle_error(e)

        except asyncio.CancelledError:
            self._log('info', f"Trading bot {self.name} main loop cancelled")
        except Exception as e:
            self._log('error', f"Fatal error in trading bot {self.name}: {e}")
            await self.handle_error(e)

    async def _process_symbol(self, symbol: str) -> None:
        """Process trading logic for a single symbol"""
        try:
            # Get market data
            market_data = await self.brokerage.get_market_data(symbol)
            self._update_activity()

            # Analyze market and get trading decision
            order_request = await self.strategy.analyze_market(market_data)

            if order_request:
                self._log('info', f"Strategy suggests order: {order_request}")

                # Execute order based on mode
                if self.mode == 'live':
                    await self._execute_order(order_request)
                else:
                    self._log('info', f"Test mode: Would place order {order_request}")

        except Exception as e:
            self._log('error', f"Error processing symbol {symbol}: {e}")
            raise

    async def _execute_order(self, order_request: OrderRequest) -> None:
        """Execute an order in live mode"""
        try:
            # Validate order size
            if order_request.quantity > self._max_position_size:
                self._log('warning', f"Order quantity {order_request.quantity} exceeds max position size {self._max_position_size}")
                return

            # Place order
            order = await self.brokerage.place_order(order_request)
            self._log('info', f"Order placed: {order}")

            # Notify strategy
            await self.strategy.on_order_filled(order)

            # Store order in database
            await self._store_order(order)

        except Exception as e:
            self._log('error', f"Failed to execute order: {e}")
            await self.strategy.on_order_rejected(order_request)
            raise

    async def get_bot_status(self) -> Dict[str, Any]:
        """Get comprehensive trading bot status"""
        base_status = await super().get_bot_status()

        # Add trading-specific status information
        trading_status = {
            'mode': self.mode,
            'order_timeout': self._order_timeout,
            'max_position_size': self._max_position_size,
            'database': {
                'connected': await self.database.is_connected() if self.database else False
            }
        }

        # Merge with base status
        base_status.update(trading_status)
        return base_status

    async def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update trading bot configuration"""
        # Call parent update_config first
        await super().update_config(new_config)

        # Update trading-specific properties
        self.mode = self._config.get('mode', 'test')
        self._order_timeout = self._config.get('order_timeout', 30)
        self._max_position_size = self._config.get('max_position_size', 1000.0)

        # Update strategy config if provided
        if 'strategy' in new_config and self.strategy:
            await self.strategy.update_config(new_config['strategy'])

    async def _store_order(self, order) -> None:
        """Store order in database"""
        try:
            query = """
                INSERT INTO orders (id, symbol, side, order_type, quantity, price, status, created_at)
                VALUES (:id, :symbol, :side, :order_type, :quantity, :price, :status, :created_at)
            """
            params = {
                'id': order.id,
                'symbol': order.symbol,
                'side': order.side.value,
                'order_type': order.order_type.value,
                'quantity': order.quantity,
                'price': order.price,
                'status': order.status,
                'created_at': order.created_at or datetime.now()
            }
            await self.database.execute(query, params)
            self._log('debug', f"Order stored in database: {order.id}")

        except Exception as e:
            self._log('error', f"Failed to store order: {e}")
            raise

    async def _cleanup(self) -> None:
        """Cleanup trading bot resources"""
        self._log('info', f"Starting cleanup for trading bot {self.name}")

        # Cleanup database connection
        try:
            if self.database:
                await self.database.disconnect()
        except Exception as e:
            self._log('error', f"Database cleanup failed: {e}")

        # Call parent cleanup for strategy and brokerage
        await super()._cleanup()
